name: flutter_travelgator
description: 'Travelgator development application'
publish_to: 'none'

version: 1.0.2+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  google_fonts: ^6.2.1
  font_awesome_flutter: ^10.1.0
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1
  equatable: ^2.0.5
  dartz: ^0.10.1
  get_it: ^8.0.3
  flutter_bloc: ^9.1.1
  shared_preferences: ^2.3.2
  http: ^1.1.0
  intl: ^0.20.2
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.3
  dio: ^5.7.0
  freezed: ^2.5.2
  freezed_annotation: ^2.4.1
  json_annotation: ^4.9.0
  go_router: ^14.2.7
  graphql_flutter: ^5.2.0
  shimmer: ^3.0.0
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  firebase_remote_config: ^5.4.3
  google_sign_in: ^6.2.1
  google_sign_in_ios: ^5.9.0
  sign_in_with_apple: ^6.1.2
  flutter_native_splash: ^2.4.6
  local_auth: ^2.1.6
  flutter_stripe: ^11.5.0
  auto_size_text: ^3.0.0
  webview_flutter: ^4.9.0  # Used by other checkout webviews
  flutter_dotenv: ^5.2.1
  url_launcher: ^6.2.2  # For Shopify checkout URL launching
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.8
  riverpod_generator: ^2.3.9
  flutter_lints: ^5.0.0
  mockito: ^5.4.4
  json_serializable: ^6.8.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/images/travelgator_logo.svg
    - assets/images/flags/
  generate: true
