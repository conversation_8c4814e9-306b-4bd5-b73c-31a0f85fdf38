# Removed Files Summary

This document tracks files that were removed during the authentication flow refactoring in branch `res/update-authenticate-flow` and isolated in branch `res/cleanup-removed-files`.

## Overview

**Total files removed:** 55 files  
**Lines of code removed:** ~10,696 lines  
**Branch created:** `res/cleanup-removed-files`  
**Commit:** `fbf36fe`

## Categories of Removed Files

### 1. Documentation Files (9 files)
- `CONTRIBUTING.md` - Contributing guidelines
- `FASTLANE_SETUP.md` - Fastlane setup documentation
- `RIVERPOD_MIGRATION.md` - Riverpod migration guide
- `docs/API_DOCUMENTATION.md` - API documentation
- `docs/api_integration_guide.md` - API integration guide
- `docs/authentication_flow.md` - Authentication flow documentation
- `docs/buy_now_documentation.md` - Buy now feature documentation
- `docs/checkout_architecture.md` - Checkout architecture documentation

### 2. Example and Shared Code (2 files)
- `lib/examples/card_input_example.dart` - Card input example implementation
- `lib/shared/widgets/example_usage.dart` - Example usage widgets

### 3. Authentication Components (6 files)
- `lib/features/auth/presentation/providers/auth_provider.dart` - Legacy auth provider
- `lib/features/auth/presentation/screens/auth_debug_screen.dart` - Auth debug screen
- `lib/features/auth/presentation/screens/auth_modal_test_screen.dart` - Auth modal test screen
- `lib/features/auth/presentation/screens/sign_in_screen.dart` - Sign in screen
- `lib/features/auth/presentation/screens/sign_up_screen.dart` - Sign up screen
- `lib/features/auth/presentation/widgets/backend_auth_debug_widget.dart` - Backend auth debug widget

### 4. Cart Components (3 files)
- `lib/features/cart/data/services/voucher_service.dart` - Legacy voucher service
- `lib/features/cart/presentation/screens/cart_auth_test_screen.dart` - Cart auth test screen
- `lib/features/cart/presentation/services/cart_checkout_service.dart` - Legacy cart checkout service

### 5. Home and Navigation Components (4 files)
- `lib/features/home/<USER>
- `lib/features/home/<USER>/widgets/app_tab_view.dart` - App tab view widget
- `lib/features/home/<USER>/widgets/main_app_tab_view.dart` - Main app tab view widget
- `lib/features/landing/presentation/screens/landing_screen.dart` - Landing screen

### 6. Legal Components (2 files)
- `lib/features/legal/presentation/screens/legal_test_screen.dart` - Legal test screen
- `lib/features/legal/presentation/screens/modal_test_screen.dart` - Modal test screen

### 7. Payment Components (11 files)
- `lib/features/payment/data/datasources/cart_payment_data_source.dart` - Cart payment data source
- `lib/features/payment/data/repositories/payment_repository_impl.dart` - Legacy payment repository
- `lib/features/payment/data/services/buy_now_voucher_service.dart` - Buy now voucher service
- `lib/features/payment/presentation/providers/buy_now_voucher_provider.dart` - Buy now voucher provider
- `lib/features/payment/presentation/screens/modular_checkout_screen.dart` - Modular checkout screen
- `lib/features/payment/presentation/widgets/buy_now_discount_code_widget.dart` - Buy now discount widget
- `lib/features/payment/presentation/widgets/checkout_bottom_section.dart` - Checkout bottom section
- `lib/features/payment/presentation/widgets/checkout_credits_discount_section.dart` - Credits discount section
- `lib/features/payment/presentation/widgets/checkout_grand_total_section.dart` - Grand total section
- `lib/features/payment/presentation/widgets/checkout_header.dart` - Checkout header
- `lib/features/payment/presentation/widgets/checkout_payment_section.dart` - Payment section
- `lib/features/payment/presentation/widgets/discount_code_widget.dart` - Discount code widget

### 8. Purchase Components (1 file)
- `lib/features/purchase/presentation/screens/checkout_webview_screen.dart` - Checkout webview screen

### 9. Test Files (17 files)

#### Core Tests (4 files)
- `test/core/errors/failures_test.dart` - Failures test
- `test/core/utils/price_formatter_test.dart` - Price formatter test
- `test/core/utils/snackbar_utils_test.dart` - Snackbar utils test
- `test/core/utils/validators_test.dart` - Validators test

#### Auth Tests (2 files)
- `test/features/auth/domain/usecases/composite_apple_sign_in_use_case_test.dart` - Apple sign in use case test
- `test/features/auth/presentation/providers/auth_provider_test.dart` - Auth provider test

#### Cart Tests (2 files)
- `test/features/cart/data/models/cart_model_test.dart` - Cart model test
- `test/features/cart/presentation/widgets/cart_bottom_info_test.dart` - Cart bottom info test

#### Payment Tests (4 files)
- `test/features/payment/new_payment_flow_test.dart` - New payment flow test
- `test/features/payment/presentation/providers/new_checkout_provider_test.dart` - New checkout provider test
- `test/features/payment/presentation/screens/checkout_screen_error_handling_test.dart` - Checkout error handling test
- `test/features/payment/presentation/widgets/card_number_formatter_test.dart` - Card number formatter test

#### Purchase Tests (5 files)
- `test/features/purchase/data/models/buy_now_response_model_test.dart` - Buy now response model test
- `test/features/purchase/pre_purchase_screen_test.dart` - Pre purchase screen test
- `test/features/purchase/presentation/widgets/purchase_actions_image_test.dart` - Purchase actions image test
- `test/features/purchase/presentation/widgets/purchase_actions_payment_intent_test.dart` - Purchase actions payment intent test

## Reason for Removal

These files were removed as part of the authentication flow refactoring to:
1. Clean up deprecated and unused code
2. Remove legacy implementations that have been replaced
3. Eliminate test files for removed components
4. Streamline the codebase by removing outdated documentation

## Branch Information

- **Source branch:** `res/update-authenticate-flow`
- **Cleanup branch:** `res/cleanup-removed-files`
- **Base branch:** `main`
- **Commit hash:** `fbf36fe`

## Next Steps

1. Review the removed files to ensure no critical functionality was lost
2. Update any remaining references to removed files
3. Consider merging the cleanup branch if the removals are approved
4. Update documentation to reflect the new architecture
