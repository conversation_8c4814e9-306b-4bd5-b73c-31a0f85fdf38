# 🚀 TravelGator App - API Endpoints We're Using

## 📍 **Base URL**
- **Development:** `http://192.168.1.26:3000/api`
- **Production:** `https://api.travelgator.com/api`

---

## 🔐 **Authentication**

### **POST /api/auth/authenticate**
**Purpose:** Login with Firebase token
**Used in:** `lib/features/auth/data/datasources/backend_auth_data_source.dart`
**When:** User signs in with Google/Apple, app startup

**Request:**
```json
{
  "firebase_id_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjY4M..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "backend_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "<PERSON>"
    }
  }
}
```

### **POST /api/auth/verify**
**Purpose:** Verify backend token
**Used in:** `lib/features/auth/data/datasources/backend_auth_data_source.dart`
**When:** Token validation during app startup

**Request:**
```json
{
  "backend_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "user_id": "user_123"
  }
}
```

## 🛒 **Cart Operations**

### **GET /api/cart**
**Purpose:** Get cart contents
**Used in:** `lib/features/cart/data/repositories/cart_repository_impl.dart`
**When:** App startup, cart screen load, after cart modifications

**Request:** No body required

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cart_123",
    "items": [
      {
        "id": "item_1",
        "variant_id": "gid://shopify/ProductVariant/12345",
        "product_id": "gid://shopify/Product/67890",
        "title": "Bali Adventure Package",
        "price": 299.99,
        "quantity": 2,
        "currency": "USD",
        "image_url": "https://example.com/image.jpg"
      }
    ],
    "total_price": 599.98,
    "currency": "USD",
    "item_count": 2,
    "has_discount": false,
    "preferred_payment_method": "stripe"
  }
}
```

### **POST /api/cart/add_item**
**Purpose:** Add item to cart
**Used in:** `lib/features/cart/data/repositories/cart_repository_impl.dart`
**When:** User clicks "Add to Cart" button

**Request:**
```json
{
  "variant_id": "12345",
  "product_id": "67890",
  "quantity": 1,
  "price": 299.99,
  "title": "Bali Adventure Package",
  "currency": "USD",
  "image_url": "https://example.com/image.jpg"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cart_123",
    "items": [...],
    "total_price": 299.99,
    "item_count": 1
  }
}
```

### **PUT /api/cart/update_item**
**Purpose:** Update item quantity
**Used in:** `lib/features/cart/data/repositories/cart_repository_impl.dart`
**When:** User changes quantity in cart

**Request:**
```json
{
  "variant_id": "12345",
  "quantity": 3
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cart_123",
    "items": [...],
    "total_price": 899.97,
    "item_count": 3
  }
}
```

### **DELETE /api/cart/remove_item**
**Purpose:** Remove item from cart
**Used in:** `lib/features/cart/data/repositories/cart_repository_impl.dart`
**When:** User clicks remove/delete item

**Request:**
```json
{
  "variant_id": "12345"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cart_123",
    "items": [],
    "total_price": 0.00,
    "item_count": 0
  }
}
```



### **GET /api/cart/payment_methods**
**Purpose:** Get available payment methods
**Used in:** `lib/features/cart/data/repositories/cart_repository_impl.dart`
**When:** Before checkout, payment method selection

**Request:** No body required

**Response:**
```json
{
  "success": true,
  "data": {
    "available_methods": ["stripe", "shopify"],
    "preferred_method": "stripe",
    "stripe_enabled": true,
    "shopify_enabled": true
  }
}
```

## 💳 **Payment Processing (MAIN)**

### **POST /api/checkout_items**
**Purpose:** Universal checkout for all payment methods
**Used in:** `lib/features/payment/data/datasources/unified_payment_data_source.dart`, `lib/features/payment/data/datasources/stripe_payment_data_source.dart`, `lib/features/payment/data/datasources/shopify_payment_data_source.dart`
**When:** Cart checkout, Buy Now, all payment flows

**Request:**
```json
{
  "payment_method": "stripe",
  "items": [
    {
      "variant_id": "12345",
      "quantity": 1,
      "price": 299.99,
      "title": "Bali Adventure Package"
    }
  ],
  "voucher_code": "DISCOUNT10"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "order_id": "order_abc123",
    "payment_intent_id": "pi_1234567890",
    "client_secret": "pi_1234567890_secret_xyz",
    "amount": 269.99,
    "currency": "USD",
    "status": "requires_payment_method",
    "checkout_url": "https://checkout.shopify.com/...",
    "voucher_applied": true,
    "discount_amount": 30.00
  }
}
```

### **POST /api/confirm/:order_id**
**Purpose:** Confirm payment after processing
**Used in:** `lib/features/payment/data/datasources/stripe_payment_data_source.dart`, `lib/features/payment/data/datasources/unified_payment_data_source.dart`
**When:** After Stripe payment processing, payment confirmation

**Request:**
```json
{
  "payment_method_id": "pm_1234567890"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "order_id": "order_abc123",
    "status": "succeeded",
    "payment_intent_id": "pi_1234567890",
    "amount_received": 269.99,
    "currency": "USD",
    "receipt_url": "https://pay.stripe.com/receipts/..."
  }
}
```

### **GET /api/status/:identifier**
**Purpose:** Check payment/order status
**Used in:** `lib/features/payment/data/datasources/unified_status_data_source.dart`, `lib/features/payment/presentation/screens/shopify_checkout_webview.dart`
**When:** Payment status polling, order verification

**Request:** No body required
**Query Parameters:** `?payment_method=stripe` (optional)

**Response:**
```json
{
  "success": true,
  "data": {
    "order_id": "order_abc123",
    "status": "succeeded",
    "payment_method": "stripe",
    "amount": 269.99,
    "currency": "USD",
    "created_at": "2025-08-23T10:30:00Z",
    "completed_at": "2025-08-23T10:31:15Z"
  }
}
```

## 🔵 **Stripe Specific**

### **POST /api/stripe/draft_items**
**Purpose:** Create draft orders for voucher support
**Used in:** `lib/features/payment/data/services/draft_order_service.dart`
**When:** Voucher application, complex pricing scenarios

**Request:**
```json
{
  "items": [
    {
      "variant_id": "12345",
      "quantity": 1,
      "price": 299.99,
      "title": "Bali Adventure Package"
    }
  ],
  "voucher_code": "DISCOUNT10"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "draft_order_id": "draft_123",
    "payment_intent_id": "pi_1234567890",
    "client_secret": "pi_1234567890_secret_xyz",
    "amount": 269.99,
    "currency": "USD",
    "voucher_applied": true,
    "discount_amount": 30.00
  }
}
```

### **GET /api/stripe/config**
**Purpose:** Get Stripe configuration
**Used in:** `lib/features/payment/data/datasources/stripe_payment_data_source.dart`
**When:** Stripe initialization, payment setup

**Request:** No body required

**Response:**
```json
{
  "success": true,
  "data": {
    "publishable_key": "pk_test_...",
    "merchant_id": "merchant_123",
    "country_code": "US",
    "currency": "USD",
    "payment_methods": ["card", "apple_pay", "google_pay"]
  }
}
```

## 🟠 **Shopify Specific**

### **~~GET /api/shopify/config~~** ❌ REMOVED
**Status:** Endpoint removed - not needed for Shopify integration
**Reason:**
- Shopify configuration is hardcoded in `lib/core/configs/graphql_config.dart`
- Client connects directly to Shopify GraphQL API (not through backend proxy)
- Backend only creates checkout URLs via `POST /api/orders` endpoint
- Unlike Stripe, Shopify doesn't require dynamic SDK initialization with backend config

**Alternative:**
- Shopify config is managed in `GraphQLConfig` class
- Use `POST /api/orders` with `payment_method: 'shopify'` to create checkout sessions

## 🎫 **Vouchers**

### **~~POST /api/vouchers/apply_to_order~~** ❌ REMOVED
**Status:** Endpoint removed from backend
**Reason:** Order-based voucher functionality has been deprecated
**Alternative:** Use cart-based voucher operations instead

### **~~DELETE /api/vouchers/remove_from_order~~** ❌ REMOVED
**Status:** Endpoint removed from backend
**Reason:** Order-based voucher functionality has been deprecated
**Alternative:** Use cart-based voucher operations instead



## 📦 **Orders**

### **GET /api/orders**
**Purpose:** Get user's order history
**Used in:** Order history screens, user account management
**When:** Order history page load, account screen access

**Request:** No body required

**Response:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order_abc123",
        "status": "completed",
        "total_amount": 269.99,
        "currency": "USD",
        "payment_method": "stripe",
        "created_at": "2025-08-23T10:30:00Z",
        "items": [
          {
            "title": "Bali Adventure Package",
            "quantity": 1,
            "price": 299.99,
            "discount": 30.00
          }
        ]
      }
    ],
    "total_count": 1,
    "page": 1,
    "per_page": 20
  }
}
```

### **GET /api/orders/:order_id**
**Purpose:** Get specific order details
**Used in:** Order detail screens, order tracking
**When:** Order detail view, order status checking

**Request:** No body required

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "order_abc123",
    "status": "completed",
    "total_amount": 269.99,
    "currency": "USD",
    "payment_method": "stripe",
    "payment_intent_id": "pi_1234567890",
    "created_at": "2025-08-23T10:30:00Z",
    "completed_at": "2025-08-23T10:31:15Z",
    "items": [...],
    "shipping_address": {...},
    "receipt_url": "https://pay.stripe.com/receipts/..."
  }
}
```

## 👤 **User Account**

### **GET /api/users/deletion_check**
**Purpose:** Check if user account can be deleted
**Used in:** `lib/features/account/data/services/delete_account_service.dart`
**When:** Account deletion flow, user account management

**Request:** No body required

**Response:**
```json
{
  "success": true,
  "data": {
    "can_delete": true,
    "pending_orders": 0,
    "active_subscriptions": 0,
    "warnings": []
  }
}
```

### **DELETE /api/users/account**
**Purpose:** Delete user account
**Used in:** `lib/features/account/data/services/delete_account_service.dart`
**When:** Account deletion confirmation

**Request:** No body required

**Response:**
```json
{
  "success": true,
  "data": {
    "deleted": true,
    "user_id": "user_123",
    "deleted_at": "2025-08-23T10:35:00Z"
  }
}
```

---

##  **Firebase Services**

### **Firebase Authentication**
**Project ID:** `travelgator`
**Used in:** `lib/firebase_options.dart`, `lib/main.dart`
**When:** App startup, user authentication

**Services:**
- **Google Sign-In** - OAuth authentication
- **Apple Sign-In** - OAuth authentication
- **ID Token Generation** - For backend authentication

### **Firebase Remote Config**
**Used in:** `lib/core/services/remote_config_service.dart`
**When:** App startup, payment method selection

**Parameters:**
```json
{
  "shop_payment": {
    "cart_payment_method": "stripe",
    "buy_now_payment_method": "stripe",
    "stripe_enabled": true,
    "shopify_enabled": true
  }
}
```

### **Firebase Analytics**
**Used in:** App-wide analytics tracking
**When:** User interactions, screen views, events

### **Firebase Crashlytics**
**Used in:** Error reporting and crash tracking
**When:** App crashes, unhandled exceptions

---

## 📊 **Final Summary**
- **Backend REST API:** 19 endpoints
- **Firebase:** 4 services
- **Total:** 23 API integrations

**Removed Endpoints (6 total):**
- ❌ `DELETE /api/cart/clear` - Cart clearing functionality
- ❌ `POST /api/vouchers/validate` - Cart-based voucher validation
- ❌ `POST /api/vouchers/apply` - Cart-based voucher application
- ❌ `DELETE /api/vouchers/remove` - Cart-based voucher removal
- ❌ `GET /api/shopify/status/:order_id` - Shopify-specific status checking
- ❌ `POST /api/shopify/complete/:order_id` - Shopify-specific order completion

**Replacement/Alternative Endpoints:**
- ❌ `POST /api/vouchers/apply_to_order` - Order-based voucher application (REMOVED)
- ❌ `DELETE /api/vouchers/remove_from_order` - Order-based voucher removal (REMOVED)
- ✅ `GET /api/status/:identifier` - Unified status checking (replaces Shopify status)

## 🎯 **Most Used Endpoints**
1. `POST /api/checkout_items` - All payments (Stripe + Shopify)
2. `GET /api/cart` - Cart operations (every screen with cart badge)
3. `GET /api/status/:identifier` - Payment status polling
4. `POST /api/cart/add_item` - Add to cart (product interactions)
5. `POST /api/auth/authenticate` - User authentication

## 🚀 **Complete Payment Flow**
```
User Action → Payment Flow
├── Cart Checkout
│   ├── 1. GET /api/cart (load cart)
│   ├── 2. POST /api/checkout_items (create payment intent)
│   ├── 3. [Stripe SDK processes payment]
│   ├── 4. POST /api/confirm/:order_id (confirm payment)
│   └── 5. GET /api/status/:identifier (verify completion)
│
└── Buy Now
    ├── 1. POST /api/checkout_items (direct checkout)
    ├── 2. [Stripe SDK processes payment]
    ├── 3. POST /api/confirm/:order_id (confirm payment)
    └── 4. GET /api/status/:identifier (verify completion)
```

## 🔄 **Common Request Headers**
```json
{
  "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

## ⚠️ **Error Response Format**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid voucher code",
    "details": {
      "field": "voucher_code",
      "reason": "expired"
    }
  }
}
```

---

*This is exactly what we're calling in the TravelGator app with real examples and usage patterns.*
