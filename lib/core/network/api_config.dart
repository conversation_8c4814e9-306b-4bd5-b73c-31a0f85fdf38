import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// API configuration for the backend authentication service
class ApiConfig {
  // Production and development base URLs
  static const String prodBaseUrl = 'https://api.travelgator.com';
  static const String localBaseUrl = 'http://192.168.1.46:3009'; // Local development

  // Current base URL - change this based on environment
  static const String baseUrl = localBaseUrl; // Change to prodBaseUrl or devBaseUrl as needed

  static const String apiVersion = '/api';

  // Authentication endpoints
  static const String authEndpoint = '/auth/authenticate';
  static const String authVerifyEndpoint = '/auth/verify';

  // Cart management endpoints - Modern unified approach
  static const String cartEndpoint = '/cart'; // Modern PUT /cart endpoint for all operations

  // Order status endpoint (updated to query-based API)
  static const String ordersStatusEndpoint = '/orders/status';

  // Stripe-specific endpoints (still needed for specialized functionality)
  static const String stripeConfigEndpoint = '/stripe/config';

  // User account endpoints
  static const String userDeletionCheckEndpoint = '/users/deletion_check';
  static const String userAccountEndpoint = '/users/account';

  // Order endpoints
  static const String ordersEndpoint = '/orders';
  
  // Timeouts
  static const int connectTimeout = 5000; // 5 seconds
  static const int receiveTimeout = 10000; // 10 seconds
  static const int sendTimeout = 10000; // 10 seconds

  /// Create and configure Dio instance for backend API calls
  static Dio createDioClient() {
    final dio = Dio();
    
    // Base options
    dio.options = BaseOptions(
      baseUrl: baseUrl + apiVersion,
      connectTimeout: Duration(milliseconds: connectTimeout),
      receiveTimeout: Duration(milliseconds: receiveTimeout),
      sendTimeout: Duration(milliseconds: sendTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // Add simplified logging interceptor in debug mode
    if (kDebugMode) {
      dio.interceptors.add(InterceptorsWrapper(
        onRequest: (options, handler) {
          debugPrint('[API] ${options.method.toUpperCase()} ${options.uri}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          debugPrint('[API] Status: ${response.statusCode}');
          debugPrint('[API] Response: ${response.data}');
          handler.next(response);
        },
        onError: (error, handler) {
          debugPrint('[API] Error: ${error.response?.statusCode} - ${error.message}');

          // For connection errors, fail fast to enable offline mode
          if (error.type == DioExceptionType.connectionTimeout ||
              error.type == DioExceptionType.connectionError ||
              error.type == DioExceptionType.receiveTimeout) {
            debugPrint('[API] Connection error detected - failing fast for offline mode');
          }

          handler.next(error);
        },
      ));
    }

    return dio;
  }

  // Convenience getters for complete URLs
  static String get authUrl => baseUrl + apiVersion + authEndpoint;
  static String get authVerifyUrl => baseUrl + apiVersion + authVerifyEndpoint;

  // Order status URL helpers for updated endpoint
  static String ordersStatusUrl(String orderId, {String? paymentMethod}) {
    final queryParameters = <String, String>{
      'order_id': orderId,
      if (paymentMethod != null && paymentMethod.isNotEmpty) 'payment_method': paymentMethod,
    };

    final query = Uri(queryParameters: queryParameters).query;
    return '$baseUrl$apiVersion$ordersStatusEndpoint?$query';
  }

  static String ordersStatusByPaymentIntentUrl(String paymentIntentId, {String? paymentMethod}) {
    final queryParameters = <String, String>{
      'payment_intent_id': paymentIntentId,
      if (paymentMethod != null && paymentMethod.isNotEmpty) 'payment_method': paymentMethod,
    };

    final query = Uri(queryParameters: queryParameters).query;
    return '$baseUrl$apiVersion$ordersStatusEndpoint?$query';
  }

  // Stripe-specific URL getters (still needed for specialized functionality)
  static String get stripeConfigUrl => baseUrl + apiVersion + stripeConfigEndpoint;

  // Cart management URLs - Modern unified approach
  static String get cartUrl => baseUrl + apiVersion + cartEndpoint; // Modern PUT /cart for all operations



  // Order URLs
  static String get ordersUrl => baseUrl + apiVersion + ordersEndpoint;

  // Unified status URLs (for payment status checking)
  static String unifiedStatusUrl(String orderId) => '$baseUrl$apiVersion$ordersStatusEndpoint?order_id=$orderId';
  static String unifiedStatusUrlWithMethod(String orderId, String paymentMethod) => '$baseUrl$apiVersion$ordersStatusEndpoint?order_id=$orderId';

  // User account URLs
  static String get userDeletionCheckUrl => baseUrl + apiVersion + userDeletionCheckEndpoint;
  static String get userAccountUrl => baseUrl + apiVersion + userAccountEndpoint;
}
