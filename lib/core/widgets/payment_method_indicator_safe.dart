import 'package:flutter/material.dart';
import '../services/payment_config_validator.dart';

/// Simple wrapper for payment method display with validation
class PaymentMethodIndicatorSafe extends StatelessWidget {
  final bool isFromBuyNow;
  
  const PaymentMethodIndicatorSafe({
    super.key,
    required this.isFromBuyNow,
  });

  @override
  Widget build(BuildContext context) {
    final paymentMethod = PaymentConfigValidator.getCurrentPaymentMethodSafe(
      isFromBuyNow: isFromBuyNow,
    );

    if (paymentMethod == null) {
      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.shade300),
        ),
        child: Row(
          children: [
            Icon(Icons.error, color: Colors.red.shade700, size: 16),
            const SizedBox(width: 8),
            const Text(
              'Payment method not configured',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: paymentMethod.isStripe ? Colors.blue.shade50 : Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: paymentMethod.isStripe ? Colors.blue.shade200 : Colors.green.shade200,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            paymentMethod.isStripe ? Icons.credit_card : Icons.shopping_cart,
            size: 20,
            color: paymentMethod.isStripe ? Colors.blue.shade700 : Colors.green.shade700,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Payment Method',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  paymentMethod.displayName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: paymentMethod.isStripe ? Colors.blue.shade700 : Colors.green.shade700,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: paymentMethod.isStripe ? Colors.blue.shade100 : Colors.green.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              paymentMethod.isStripe ? 'In-App' : 'Browser',
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: paymentMethod.isStripe ? Colors.blue.shade800 : Colors.green.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
