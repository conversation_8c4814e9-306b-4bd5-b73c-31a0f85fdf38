import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../services/payment_config_validator.dart';

/// Widget that validates payment configuration before allowing checkout
class PaymentConfigCheckWidget extends StatefulWidget {
  final bool isFromBuyNow;
  final Widget child;
  final VoidCallback? onConfigurationError;
  
  const PaymentConfigCheckWidget({
    super.key,
    required this.isFromBuyNow,
    required this.child,
    this.onConfigurationError,
  });

  @override
  State<PaymentConfigCheckWidget> createState() => _PaymentConfigCheckWidgetState();
}

class _PaymentConfigCheckWidgetState extends State<PaymentConfigCheckWidget> {

  @override
  void initState() {
    super.initState();
    _validateConfiguration();
  }

  Future<void> _validateConfiguration() async {
    try {
      final result = await PaymentConfigValidator.validatePaymentConfig(
        isFromBuyNow: widget.isFromBuyNow,
      );

      if (mounted && !result.isValid) {
        // Redirect to home with notification instead of showing error screen
        _redirectToHomeWithNotification(result.error ?? 'Payment configuration error');
      }
    } catch (e) {
      if (mounted) {
        // Redirect to home with notification instead of showing error screen
        _redirectToHomeWithNotification(e.toString());
      }
    }
  }

  /// Redirect to home and show notification about payment configuration error
  void _redirectToHomeWithNotification(String error) {
    if (!mounted) return;

    // Navigate to home
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.go('/home');

        // Show notification
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Payment setup required. Please contact support.',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Always show the child widget since we handle errors by redirecting
    // If validation is in progress or failed, we redirect to home
    return widget.child;
  }
}
