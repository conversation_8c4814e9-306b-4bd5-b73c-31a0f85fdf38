import 'dart:async';
import 'dart:math' as math;
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_stripe/flutter_stripe.dart' as stripe;

/// Service for handling payment error recovery patterns
/// Implements network failure recovery, payment failure recovery, and session expiration handling
/// as specified in the payment integration guide
class PaymentErrorRecoveryService {
  
  /// Network failure recovery with exponential backoff
  /// Maximum 3 retries with delays: 1s, 2s, 4s
  static Future<T> retryWithExponentialBackoff<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    String operationName = 'Operation',
  }) async {
    int attempt = 0;
    
    while (attempt <= maxRetries) {
      try {
        debugPrint('[ErrorRecovery] $operationName attempt ${attempt + 1}/${maxRetries + 1}');
        return await operation();
      } catch (e) {
        attempt++;
        
        if (attempt > maxRetries) {
          debugPrint('[ErrorRecovery] $operationName failed after $maxRetries retries: $e');
          rethrow;
        }
        
        // Check if this is a retryable error
        if (!_isRetryableError(e)) {
          debugPrint('[ErrorRecovery] $operationName failed with non-retryable error: $e');
          rethrow;
        }
        
        // Calculate exponential backoff delay: 1s, 2s, 4s
        final delaySeconds = math.pow(2, attempt - 1).toInt();
        final delay = Duration(seconds: delaySeconds);
        
        debugPrint('[ErrorRecovery] $operationName failed, retrying in ${delay.inSeconds}s... (attempt $attempt)');
        debugPrint('[ErrorRecovery] Error: $e');
        
        await Future.delayed(delay);
      }
    }
    
    throw Exception('$operationName failed after $maxRetries retries');
  }

  /// Check if an error is retryable
  static bool _isRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Network-related errors that should be retried
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
        case DioExceptionType.connectionError:
          return true;
        case DioExceptionType.badResponse:
          // Retry on server errors (5xx) but not client errors (4xx)
          final statusCode = error.response?.statusCode;
          return statusCode != null && statusCode >= 500;
        default:
          return false;
      }
    }
    
    // Network-related error messages
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('socket')) {
      return true;
    }
    
    // Server errors
    if (errorString.contains('server error') ||
        errorString.contains('internal error') ||
        errorString.contains('service unavailable')) {
      return true;
    }
    
    return false;
  }

  /// Handle payment failure recovery
  static PaymentFailureRecoveryAction handlePaymentFailure(dynamic error) {
    debugPrint('[ErrorRecovery] Analyzing payment failure: $error');
    
    if (error is stripe.StripeException) {
      return _handleStripeError(error);
    }
    
    if (error is DioException) {
      return _handleNetworkError(error);
    }
    
    final errorString = error.toString().toLowerCase();
    
    // Session expiration
    if (errorString.contains('authentication required') ||
        errorString.contains('unauthorized') ||
        errorString.contains('token expired') ||
        errorString.contains('invalid token')) {
      return PaymentFailureRecoveryAction(
        type: PaymentFailureType.sessionExpired,
        userMessage: 'Your session has expired. Please log in again.',
        allowRetry: false,
        suggestedAction: 'Please log in again and try your payment.',
      );
    }
    
    // Payment method issues
    if (errorString.contains('card declined') ||
        errorString.contains('insufficient funds') ||
        errorString.contains('payment method')) {
      return PaymentFailureRecoveryAction(
        type: PaymentFailureType.paymentMethodIssue,
        userMessage: 'Payment method issue. Please try a different card.',
        allowRetry: true,
        suggestedAction: 'Try a different payment method or contact your bank.',
      );
    }
    
    // Network issues
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return PaymentFailureRecoveryAction(
        type: PaymentFailureType.networkError,
        userMessage: 'Network error. Please check your connection and try again.',
        allowRetry: true,
        suggestedAction: 'Check your internet connection and retry.',
      );
    }
    
    // Generic failure
    return PaymentFailureRecoveryAction(
      type: PaymentFailureType.unknown,
      userMessage: 'Payment failed. Please try again.',
      allowRetry: true,
      suggestedAction: 'Please try again or contact support if the issue persists.',
    );
  }

  /// Handle Stripe-specific errors
  static PaymentFailureRecoveryAction _handleStripeError(stripe.StripeException error) {
    debugPrint('[ErrorRecovery] Handling Stripe error: ${error.error.code}');
    
    switch (error.error.code) {
      case stripe.FailureCode.Canceled:
        return PaymentFailureRecoveryAction(
          type: PaymentFailureType.userCanceled,
          userMessage: 'Payment was canceled.',
          allowRetry: true,
          suggestedAction: 'You can try again when ready.',
        );
        
      case stripe.FailureCode.Failed:
        final message = error.error.localizedMessage ?? error.error.message ?? 'Payment failed';
        return PaymentFailureRecoveryAction(
          type: PaymentFailureType.paymentMethodIssue,
          userMessage: message,
          allowRetry: true,
          suggestedAction: 'Please try a different payment method or contact your bank.',
        );
        
      default:
        return PaymentFailureRecoveryAction(
          type: PaymentFailureType.unknown,
          userMessage: error.error.localizedMessage ?? error.error.message ?? 'Payment error occurred',
          allowRetry: true,
          suggestedAction: 'Please try again or contact support.',
        );
    }
  }

  /// Handle network-related errors
  static PaymentFailureRecoveryAction _handleNetworkError(DioException error) {
    debugPrint('[ErrorRecovery] Handling network error: ${error.type}');
    
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return PaymentFailureRecoveryAction(
          type: PaymentFailureType.networkError,
          userMessage: 'Connection timeout. Please try again.',
          allowRetry: true,
          suggestedAction: 'Check your internet connection and retry.',
        );
        
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        if (statusCode == 401) {
          return PaymentFailureRecoveryAction(
            type: PaymentFailureType.sessionExpired,
            userMessage: 'Your session has expired. Please log in again.',
            allowRetry: false,
            suggestedAction: 'Please log in again and try your payment.',
          );
        } else if (statusCode != null && statusCode >= 500) {
          return PaymentFailureRecoveryAction(
            type: PaymentFailureType.serverError,
            userMessage: 'Server error. Please try again in a moment.',
            allowRetry: true,
            suggestedAction: 'Please wait a moment and try again.',
          );
        }
        break;
        
      case DioExceptionType.connectionError:
        return PaymentFailureRecoveryAction(
          type: PaymentFailureType.networkError,
          userMessage: 'Connection error. Please check your internet connection.',
          allowRetry: true,
          suggestedAction: 'Check your internet connection and retry.',
        );
        
      default:
        break;
    }
    
    return PaymentFailureRecoveryAction(
      type: PaymentFailureType.unknown,
      userMessage: 'Network error occurred. Please try again.',
      allowRetry: true,
      suggestedAction: 'Please try again or contact support.',
    );
  }

  /// Check if session has expired and needs refresh
  static bool isSessionExpired(dynamic error) {
    if (error is DioException && error.response?.statusCode == 401) {
      return true;
    }
    
    final errorString = error.toString().toLowerCase();
    return errorString.contains('authentication required') ||
           errorString.contains('unauthorized') ||
           errorString.contains('token expired') ||
           errorString.contains('invalid token');
  }
}

/// Types of payment failures
enum PaymentFailureType {
  networkError,
  sessionExpired,
  paymentMethodIssue,
  serverError,
  userCanceled,
  unknown,
}

/// Recovery action for payment failures
class PaymentFailureRecoveryAction {
  final PaymentFailureType type;
  final String userMessage;
  final bool allowRetry;
  final String suggestedAction;

  PaymentFailureRecoveryAction({
    required this.type,
    required this.userMessage,
    required this.allowRetry,
    required this.suggestedAction,
  });

  @override
  String toString() {
    return 'PaymentFailureRecoveryAction(type: $type, userMessage: $userMessage, allowRetry: $allowRetry)';
  }
}
