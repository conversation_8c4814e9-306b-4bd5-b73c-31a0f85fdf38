import 'dart:async';
import 'dart:convert';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import '../enums/payment_method.dart';
import '../network/api_config.dart';
import '../../features/payment/data/datasources/stripe_payment_data_source.dart';

/// Service for managing Firebase Remote Config
/// Controls dynamic payment method switching between Stripe and Shopify
/// STRICT MODE: Requires Remote Config parameters to be set up properly
class RemoteConfigService {
  static final FirebaseRemoteConfig _remoteConfig = FirebaseRemoteConfig.instance;

  // Remote Config parameter key - single JSON parameter
  static const String _shopPaymentKey = 'shop_payment';

  // Cache for parsed payment config
  static Map<String, dynamic>? _paymentConfig;

  // Cache for Stripe configuration (fetched from backend)
  static Map<String, dynamic>? _stripeConfig;
  static DateTime? _configCacheTime;
  static const Duration _configCacheDuration = Duration(minutes: 15);

  // Track initialization status
  static bool _isInitialized = false;

  // Stream controller for payment method changes
  static final StreamController<PaymentMethodChangeEvent> _paymentMethodChangeController =
      StreamController<PaymentMethodChangeEvent>.broadcast();

  // Subscription to remote config updates
  static StreamSubscription<RemoteConfigUpdate>? _configUpdateSubscription;

  /// Initialize Remote Config and REQUIRE values from Firebase
  static Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('[RemoteConfig] Already initialized, skipping...');
      return;
    }

    try {
      debugPrint('[RemoteConfig] Initializing Firebase Remote Config...');
      debugPrint('[RemoteConfig] ⚠️ STRICT MODE: Remote Config parameters are REQUIRED');

      // Configure Remote Config settings for immediate fetching
      await _remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(seconds: 0), // Allow immediate fetching for testing
        ),
      );

      // 🚨 CRITICAL: Fetch from Firebase - NO DEFAULTS SET
      debugPrint('[RemoteConfig] 🔄 Fetching REQUIRED values from Firebase Console...');
      final bool activated = await _remoteConfig.fetchAndActivate();

      // 🚨 VALIDATE: Check if required parameters exist in Firebase
      await _validateRequiredParameters();

      _isInitialized = true;

      if (activated) {
        debugPrint('[RemoteConfig] ✅ Successfully fetched and activated Firebase values');
      } else {
        debugPrint('[RemoteConfig] ℹ️ Using cached Firebase values (no new changes)');
      }

      // Log the actual values being used (must be from Firebase)
      final cartMethod = getCartPaymentMethod();
      final buyNowMethod = getBuyNowPaymentMethod();

      debugPrint('[RemoteConfig] 📊 Current Configuration:');
      debugPrint('[RemoteConfig]   Cart payment method: ${cartMethod.displayName}');
      debugPrint('[RemoteConfig]   Buy now payment method: ${buyNowMethod.displayName}');
      debugPrint('[RemoteConfig]   Source: Firebase Remote Config (REQUIRED)');

      // Start listening for real-time updates
      startListeningForUpdates();

      // Mark as initialized
      _isInitialized = true;

    } catch (e) {
      debugPrint('[RemoteConfig] ❌ CRITICAL ERROR: $e');
      rethrow; // Don't continue - let the app handle the error
    }
  }

  /// Validate that required JSON parameter exists and parse it
  static Future<void> _validateRequiredParameters() async {
    final jsonString = _remoteConfig.getString(_shopPaymentKey);

    // Check if JSON parameter exists
    if (jsonString.isEmpty) {
      throw Exception(
        'MISSING REQUIRED PARAMETER: "$_shopPaymentKey" not found in Firebase Remote Config. '
        'Please add this parameter with JSON value: {"payment_method_cart": "stripe", "payment_method_buy_now": "stripe"}'
      );
    }

    // Parse and validate JSON
    try {
      final Map<String, dynamic> config = jsonDecode(jsonString);

      // Check required fields
      if (!config.containsKey('payment_method_cart')) {
        throw Exception(
          'MISSING FIELD: "payment_method_cart" not found in shop_payment JSON. '
          'Required format: {"payment_method_cart": "stripe", "payment_method_buy_now": "stripe"}'
        );
      }

      if (!config.containsKey('payment_method_buy_now')) {
        throw Exception(
          'MISSING FIELD: "payment_method_buy_now" not found in shop_payment JSON. '
          'Required format: {"payment_method_cart": "stripe", "payment_method_buy_now": "stripe"}'
        );
      }

      // Validate values
      final cartValue = config['payment_method_cart']?.toString().toLowerCase();
      final buyNowValue = config['payment_method_buy_now']?.toString().toLowerCase();

      if (!['stripe', 'shopify'].contains(cartValue)) {
        throw Exception(
          'INVALID VALUE: payment_method_cart has value "$cartValue". Must be "stripe" or "shopify".'
        );
      }

      if (!['stripe', 'shopify'].contains(buyNowValue)) {
        throw Exception(
          'INVALID VALUE: payment_method_buy_now has value "$buyNowValue". Must be "stripe" or "shopify".'
        );
      }

      // Cache the parsed config
      _paymentConfig = config;

      debugPrint('[RemoteConfig] ✅ JSON parameter validated and parsed successfully');
      debugPrint('[RemoteConfig] Config: $config');

    } catch (e) {
      if (e is FormatException) {
        throw Exception(
          'INVALID JSON: shop_payment parameter contains invalid JSON. '
          'Required format: {"payment_method_cart": "stripe", "payment_method_buy_now": "stripe"}'
        );
      }
      rethrow;
    }
  }

  /// Get payment method for cart checkout (REQUIRES Firebase Remote Config)
  static PaymentMethod getCartPaymentMethod() {
    final config = _getPaymentConfig();
    final method = config['payment_method_cart']?.toString();

    if (method == null || method.isEmpty) {
      throw Exception(
        'REMOTE CONFIG ERROR: payment_method_cart not found in shop_payment JSON. '
        'Please set up Firebase Remote Config with proper JSON structure.'
      );
    }

    return PaymentMethod.fromString(method);
  }

  /// Get payment method for buy now flow (REQUIRES Firebase Remote Config)
  static PaymentMethod getBuyNowPaymentMethod() {
    final config = _getPaymentConfig();
    final method = config['payment_method_buy_now']?.toString();

    if (method == null || method.isEmpty) {
      throw Exception(
        'REMOTE CONFIG ERROR: payment_method_buy_now not found in shop_payment JSON. '
        'Please set up Firebase Remote Config with proper JSON structure.'
      );
    }

    return PaymentMethod.fromString(method);
  }

  /// Get and parse payment configuration from Remote Config
  static Map<String, dynamic> _getPaymentConfig() {
    // Return cached config if available
    if (_paymentConfig != null) {
      return _paymentConfig!;
    }

    // Parse from Remote Config
    final jsonString = _remoteConfig.getString(_shopPaymentKey);

    if (jsonString.isEmpty) {
      throw Exception(
        'REMOTE CONFIG ERROR: shop_payment parameter not found. '
        'Please add shop_payment parameter with JSON value in Firebase Console.'
      );
    }

    try {
      _paymentConfig = jsonDecode(jsonString);
      return _paymentConfig!;
    } catch (e) {
      throw Exception(
        'REMOTE CONFIG ERROR: Invalid JSON in shop_payment parameter. '
        'Expected format: {"payment_method_cart": "stripe", "payment_method_buy_now": "stripe"}'
      );
    }
  }

  /// Stream for real-time config updates
  static Stream<RemoteConfigUpdate> get onConfigUpdated =>
      _remoteConfig.onConfigUpdated;

  /// Manually refresh Remote Config values from Firebase
  static Future<void> refresh() async {
    try {
      debugPrint('[RemoteConfig] 🔄 Refreshing configuration from Firebase...');

      // Clear cached config
      _paymentConfig = null;
      _clearPaymentMethodConfigs();

      final bool activated = await _remoteConfig.fetchAndActivate();

      if (activated) {
        debugPrint('[RemoteConfig] ✅ New values fetched and activated from Firebase');
      } else {
        debugPrint('[RemoteConfig] ℹ️ No new values available, using existing configuration');
      }

      // Validate and cache new configuration
      await _validateRequiredParameters();

      // Log current configuration
      debugPrint('[RemoteConfig] 📊 Updated Configuration:');
      debugPrint('[RemoteConfig]   Cart: ${getCartPaymentMethod().displayName}');
      debugPrint('[RemoteConfig]   Buy now: ${getBuyNowPaymentMethod().displayName}');
      debugPrint('[RemoteConfig]   Last fetch time: ${DateTime.now()}');
    } catch (e) {
      debugPrint('[RemoteConfig] ❌ Refresh failed: $e');
      rethrow;
    }
  }

  /// Refresh Remote Config when app comes to foreground
  static Future<void> refreshOnAppResume() async {
    try {
      debugPrint('[RemoteConfig] 🔄 App resumed - checking for config updates...');

      // Only refresh if it's been more than 5 minutes since last fetch
      final lastFetchTime = _remoteConfig.lastFetchTime;
      final now = DateTime.now();
      final timeSinceLastFetch = now.difference(lastFetchTime);

      if (timeSinceLastFetch.inMinutes >= 5) {
        debugPrint('[RemoteConfig] Last fetch was ${timeSinceLastFetch.inMinutes} minutes ago - refreshing...');
        await refresh();
      } else {
        debugPrint('[RemoteConfig] Last fetch was ${timeSinceLastFetch.inMinutes} minutes ago - skipping refresh');
      }
    } catch (e) {
      debugPrint('[RemoteConfig] ❌ App resume refresh failed: $e');
      // Don't rethrow - app should continue working even if refresh fails
    }
  }

  /// Refresh Remote Config when payment fails (in case config was updated)
  static Future<void> refreshOnPaymentError() async {
    try {
      debugPrint('[RemoteConfig] 💳 Payment error - checking for config updates...');

      // Always refresh on payment errors in case config was just updated
      await refresh();

      debugPrint('[RemoteConfig] ✅ Config refreshed after payment error');
    } catch (e) {
      debugPrint('[RemoteConfig] ❌ Payment error refresh failed: $e');
      // Don't rethrow - we want to show the original payment error
    }
  }

  /// Get all current Remote Config values for debugging
  static Map<String, String> getAllValues() {
    return {
      'cart_payment_method': getCartPaymentMethod().value,
      'buy_now_payment_method': getBuyNowPaymentMethod().value,
    };
  }

  /// Get Remote Config status information
  static Map<String, dynamic> getConfigStatus() {
    try {
      final config = _getPaymentConfig();
      return {
        'last_fetch_time': _remoteConfig.lastFetchTime,
        'last_fetch_status': _remoteConfig.lastFetchStatus.toString(),
        'shop_payment_source': _remoteConfig.getValue(_shopPaymentKey).source.toString(),
        'cart_value': config['payment_method_cart'],
        'buy_now_value': config['payment_method_buy_now'],
        'json_config': config,
        'is_initialized': _isInitialized,
      };
    } catch (e) {
      return {
        'last_fetch_time': _remoteConfig.lastFetchTime,
        'last_fetch_status': _remoteConfig.lastFetchStatus.toString(),
        'error': e.toString(),
        'is_initialized': _isInitialized,
      };
    }
  }

  /// Check if values are coming from Remote Config (not defaults)
  static bool isUsingRemoteValues() {
    try {
      final shopPaymentSource = _remoteConfig.getValue(_shopPaymentKey).source;
      // ValueSource.remote means it's from Firebase Remote Config
      return shopPaymentSource.toString().contains('remote');
    } catch (e) {
      return false;
    }
  }

  /// Get raw Remote Config value for debugging
  static String getRawConfigValue() {
    try {
      return _remoteConfig.getString(_shopPaymentKey);
    } catch (e) {
      return 'Error: $e';
    }
  }

  /// Get all Remote Config keys for debugging
  static Set<String> getAllConfigKeys() {
    try {
      return _remoteConfig.getAll().keys.toSet();
    } catch (e) {
      return {'Error: $e'};
    }
  }

  /// Check if Remote Config has been initialized
  static bool isInitialized() => _isInitialized;

  /// Start listening for real-time config updates
  static void startListeningForUpdates() {
    if (_configUpdateSubscription != null) {
      debugPrint('[RemoteConfig] Already listening for updates');
      return;
    }

    debugPrint('[RemoteConfig] 🔄 Starting real-time config update listener...');
    _configUpdateSubscription = _remoteConfig.onConfigUpdated.listen(
      (RemoteConfigUpdate event) async {
        debugPrint('[RemoteConfig] 📡 Config update received: ${event.updatedKeys}');

        if (event.updatedKeys.contains(_shopPaymentKey)) {
          debugPrint('[RemoteConfig] 💳 Payment configuration updated!');

          // Store previous values
          final previousCartMethod = _getPaymentMethodSafely(false);
          final previousBuyNowMethod = _getPaymentMethodSafely(true);

          try {
            // Activate new values
            await _remoteConfig.activate();

            // Clear cache to force re-parsing
            _paymentConfig = null;

            // Validate new configuration
            await _validateRequiredParameters();

            // Get new values
            final newCartMethod = getCartPaymentMethod();
            final newBuyNowMethod = getBuyNowPaymentMethod();

            // Notify listeners of changes
            if (previousCartMethod != newCartMethod) {
              _paymentMethodChangeController.add(PaymentMethodChangeEvent(
                flowType: PaymentFlowType.cart,
                previousMethod: previousCartMethod,
                newMethod: newCartMethod,
              ));
            }

            if (previousBuyNowMethod != newBuyNowMethod) {
              _paymentMethodChangeController.add(PaymentMethodChangeEvent(
                flowType: PaymentFlowType.buyNow,
                previousMethod: previousBuyNowMethod,
                newMethod: newBuyNowMethod,
              ));
            }

            debugPrint('[RemoteConfig] ✅ Payment configuration updated successfully');
            debugPrint('[RemoteConfig]   Cart: ${newCartMethod.displayName}');
            debugPrint('[RemoteConfig]   Buy now: ${newBuyNowMethod.displayName}');

          } catch (e) {
            debugPrint('[RemoteConfig] ❌ Failed to process config update: $e');
          }
        }
      },
      onError: (error) {
        debugPrint('[RemoteConfig] ❌ Config update error: $error');
      },
    );
  }

  /// Stop listening for config updates
  static void stopListeningForUpdates() {
    _configUpdateSubscription?.cancel();
    _configUpdateSubscription = null;
    debugPrint('[RemoteConfig] 🛑 Stopped listening for config updates');
  }

  /// Stream of payment method changes
  static Stream<PaymentMethodChangeEvent> get onPaymentMethodChanged =>
      _paymentMethodChangeController.stream;

  /// Safely get payment method without throwing exceptions
  static PaymentMethod _getPaymentMethodSafely(bool isFromBuyNow) {
    try {
      return isFromBuyNow ? getBuyNowPaymentMethod() : getCartPaymentMethod();
    } catch (e) {
      return PaymentMethod.stripe; // Default fallback
    }
  }

  /// Dispose resources
  static void dispose() {
    stopListeningForUpdates();
    _paymentMethodChangeController.close();
  }
  /// Get Stripe configuration from backend
  /// Uses caching to avoid frequent API calls
  static Future<Map<String, dynamic>?> getStripeConfig(String authToken) async {
    try {
      // Check cache first
      if (_stripeConfig != null && _isConfigCacheValid()) {
        debugPrint('[RemoteConfig] 📋 Using cached Stripe config');
        return _stripeConfig;
      }

      debugPrint('[RemoteConfig] 🔄 Fetching Stripe config from backend...');

      final dio = ApiConfig.createDioClient();
      final dataSource = StripePaymentDataSourceImpl(dio: dio);

      final config = await dataSource.getStripeConfig(authToken: authToken);

      // Cache the configuration
      _stripeConfig = config.toJson();
      _configCacheTime = DateTime.now();

      debugPrint('[RemoteConfig] ✅ Stripe config fetched and cached');
      return _stripeConfig;
    } catch (e) {
      debugPrint('[RemoteConfig] ❌ Failed to fetch Stripe config: $e');
      return _stripeConfig; // Return cached config if available
    }
  }

  /// Clear payment method configuration cache
  static void _clearPaymentMethodConfigs() {
    _stripeConfig = null;
    _configCacheTime = null;
    debugPrint('[RemoteConfig] 🗑️ Payment method config cache cleared');
  }

  /// Check if configuration cache is still valid
  static bool _isConfigCacheValid() {
    if (_configCacheTime == null) return false;

    final now = DateTime.now();
    final cacheAge = now.difference(_configCacheTime!);

    return cacheAge < _configCacheDuration;
  }

  /// Force refresh Stripe configuration from backend
  /// Note: Shopify config is hardcoded in GraphQLConfig, no need to refresh
  static Future<void> refreshStripeConfig(String authToken) async {
    debugPrint('[RemoteConfig] 🔄 Force refreshing Stripe config...');

    _clearPaymentMethodConfigs();
    await getStripeConfig(authToken);

    debugPrint('[RemoteConfig] ✅ Stripe config refreshed');
  }
}

/// Event class for payment method changes
class PaymentMethodChangeEvent {
  final PaymentFlowType flowType;
  final PaymentMethod previousMethod;
  final PaymentMethod newMethod;

  PaymentMethodChangeEvent({
    required this.flowType,
    required this.previousMethod,
    required this.newMethod,
  });

  @override
  String toString() {
    return 'PaymentMethodChangeEvent(flowType: $flowType, '
           'previousMethod: ${previousMethod.displayName}, '
           'newMethod: ${newMethod.displayName})';
  }
}

/// Enum for payment flow types
enum PaymentFlowType {
  cart,
  buyNow;

  String get displayName {
    switch (this) {
      case PaymentFlowType.cart:
        return 'Cart Checkout';
      case PaymentFlowType.buyNow:
        return 'Buy Now';
    }
  }
}