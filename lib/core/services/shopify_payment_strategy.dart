import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/payment/domain/entities/payment_intent.dart';
import '../../features/auth/presentation/providers/auth_providers.dart';
import '../../features/auth/data/models/enhanced_user_model.dart';
import '../enums/payment_method.dart';
import 'payment_strategy.dart';

/// Shopify payment strategy implementation
/// Handles Shopify hosted checkout using new payment-method-specific endpoints
class ShopifyPaymentStrategy implements PaymentStrategy {
  final WidgetRef ref;
  final BuildContext context;
  final VoidCallback? onLoadingEnd;

  ShopifyPaymentStrategy(this.ref, this.context, {this.onLoadingEnd});

  @override
  PaymentMethod get paymentMethod => PaymentMethod.shopify;

  @override
  Future<PaymentIntent> createPaymentIntent(PaymentRequest request) async {
    debugPrint('[ShopifyStrategy] Creating Shopify checkout using direct checkout (no draft order needed)');
    debugPrint('[ShopifyStrategy] Items count: ${request.items.length}');
    debugPrint('[ShopifyStrategy] Total amount: \$${request.totalAmount}');
    debugPrint('[ShopifyStrategy] Is from buy now: ${request.isFromBuyNow}');

    try {
      // Get auth token from authenticated user
      final authState = ref.read(authNotifierProvider);
      final user = authState.user;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // For EnhancedUserModel, use backend token; otherwise use Firebase token
      String? authToken;
      if (user is EnhancedUserModel && user.backendToken != null) {
        authToken = user.backendToken;
      } else {
        authToken = user.token;
      }

      if (authToken == null || authToken.isEmpty) {
        throw Exception('Authentication token not available');
      }

      // Unified checkout endpoint has been retired
      debugPrint('[ShopifyStrategy] Unified checkout endpoint is no longer available');
      throw Exception('The /api/checkout_items endpoint has been removed by the backend and is no longer available.');
    } catch (e) {
      debugPrint('[ShopifyStrategy] ❌ Error creating Shopify checkout: $e');
      rethrow;
    }
  }

  @override
  Future<PaymentResult> processPayment(PaymentRequest request) async {
    debugPrint('[ShopifyStrategy] Processing Shopify payment using direct checkout');

    try {
      // Create Shopify checkout directly (no draft order step)
      final paymentIntent = await createPaymentIntent(request);
      debugPrint('[ShopifyStrategy] ✅ Shopify checkout created: ${paymentIntent.orderId}');

      // Extract checkout URL from clientSecret (where we stored it)
      final checkoutUrl = paymentIntent.clientSecret;

      if (checkoutUrl.isNotEmpty && checkoutUrl.startsWith('http')) {
        debugPrint('[ShopifyStrategy] 🔗 Opening Shopify checkout in WebView: ${checkoutUrl.substring(0, 50)}...');

        // Complete successfully without navigation (like cart checkout does)
        debugPrint('[ShopifyStrategy] Checkout created successfully, completing without navigation requirement');

        return PaymentResult.success({
          'payment_intent': paymentIntent,
          'checkout_url': checkoutUrl,
          'requires_confirmation': false, // Shopify handles everything
          'order_id': paymentIntent.orderId,
          'payment_method': 'shopify',
          // ✅ No requires_navigation flag - let parent handle navigation like cart does
        });
      } else {
        return PaymentResult.failure('No valid checkout URL found in Shopify response');
      }
    } catch (e) {
      debugPrint('[ShopifyStrategy] ❌ Payment processing failed: $e');
      return PaymentResult.failure(e.toString());
    }
  }






}
