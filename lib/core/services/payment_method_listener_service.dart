import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'remote_config_service.dart';

/// Service to listen for payment method changes from Remote Config
/// and notify the app to update payment flows accordingly
class PaymentMethodListenerService {
  static StreamSubscription<PaymentMethodChangeEvent>? _subscription;
  static bool _isListening = false;

  /// Start listening for payment method changes
  static void startListening() {
    if (_isListening) {
      debugPrint('[PaymentMethodListener] Already listening for payment method changes');
      return;
    }

    debugPrint('[PaymentMethodListener] 🎧 Starting payment method change listener...');
    
    _subscription = RemoteConfigService.onPaymentMethodChanged.listen(
      (PaymentMethodChangeEvent event) {
        debugPrint('[PaymentMethodListener] 🔄 Payment method changed: $event');
        _handlePaymentMethodChange(event);
      },
      onError: (error) {
        debugPrint('[PaymentMethodListener] ❌ Error listening for payment method changes: $error');
      },
    );

    _isListening = true;
    debugPrint('[PaymentMethodListener] ✅ Payment method listener started');
  }

  /// Stop listening for payment method changes
  static void stopListening() {
    if (!_isListening) {
      return;
    }

    _subscription?.cancel();
    _subscription = null;
    _isListening = false;
    debugPrint('[PaymentMethodListener] 🛑 Payment method listener stopped');
  }

  /// Handle payment method changes
  static void _handlePaymentMethodChange(PaymentMethodChangeEvent event) {
    debugPrint('[PaymentMethodListener] 💳 Processing payment method change...');
    debugPrint('[PaymentMethodListener]   Flow: ${event.flowType.displayName}');
    debugPrint('[PaymentMethodListener]   From: ${event.previousMethod.displayName}');
    debugPrint('[PaymentMethodListener]   To: ${event.newMethod.displayName}');

    // Here you can add specific logic to handle payment method changes
    // For example:
    // - Clear cached payment intents
    // - Update UI state
    // - Notify active payment flows
    // - Log analytics events

    switch (event.flowType) {
      case PaymentFlowType.cart:
        _handleCartPaymentMethodChange(event);
        break;
      case PaymentFlowType.buyNow:
        _handleBuyNowPaymentMethodChange(event);
        break;
    }
  }

  /// Handle cart payment method changes
  static void _handleCartPaymentMethodChange(PaymentMethodChangeEvent event) {
    debugPrint('[PaymentMethodListener] 🛒 Cart payment method changed');
    
    // Add specific cart payment method change handling here
    // For example:
    // - Clear any cached cart payment intents
    // - Update cart checkout UI
    // - Notify cart providers
    
    _logPaymentMethodChange('Cart', event);
  }

  /// Handle buy now payment method changes
  static void _handleBuyNowPaymentMethodChange(PaymentMethodChangeEvent event) {
    debugPrint('[PaymentMethodListener] ⚡ Buy now payment method changed');
    
    // Add specific buy now payment method change handling here
    // For example:
    // - Clear any cached buy now payment intents
    // - Update buy now UI
    // - Notify buy now providers
    
    _logPaymentMethodChange('Buy Now', event);
  }

  /// Log payment method changes for analytics/debugging
  static void _logPaymentMethodChange(String flowName, PaymentMethodChangeEvent event) {
    debugPrint('[PaymentMethodListener] 📊 $flowName payment method change logged:');
    debugPrint('[PaymentMethodListener]   Previous: ${event.previousMethod.displayName}');
    debugPrint('[PaymentMethodListener]   New: ${event.newMethod.displayName}');
    debugPrint('[PaymentMethodListener]   Timestamp: ${DateTime.now()}');
    
    // Here you could send analytics events to track payment method changes
    // For example:
    // Analytics.track('payment_method_changed', {
    //   'flow_type': event.flowType.name,
    //   'previous_method': event.previousMethod.value,
    //   'new_method': event.newMethod.value,
    //   'timestamp': DateTime.now().toIso8601String(),
    // });
  }

  /// Check if currently listening
  static bool get isListening => _isListening;

  /// Dispose resources
  static void dispose() {
    stopListening();
  }
}

/// Provider for payment method listener service
final paymentMethodListenerServiceProvider = Provider<PaymentMethodListenerService>((ref) {
  // Start listening when the provider is created
  PaymentMethodListenerService.startListening();
  
  // Stop listening when the provider is disposed
  ref.onDispose(() {
    PaymentMethodListenerService.stopListening();
  });
  
  return PaymentMethodListenerService();
});
