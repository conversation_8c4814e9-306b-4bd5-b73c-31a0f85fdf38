import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_providers.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_state.dart';
import 'package:flutter_travelgator/features/auth/data/models/enhanced_user_model.dart';
import 'package:flutter_travelgator/features/cart/presentation/providers/cart_provider.dart';

/// Service to handle automatic cart initialization when user becomes authenticated
class CartInitializationService {
  final Ref _ref;
  bool _hasInitializedCart = false;
  String? _lastAuthenticatedUserId;

  CartInitializationService(this._ref) {
    _setupAuthListener();
  }

  /// Set up listener for authentication state changes
  void _setupAuthListener() {
    // Listen to auth state changes
    _ref.listen<AuthState>(
      authNotifierProvider,
      (previous, next) {
        _handleAuthStateChange(previous, next);
      },
    );

    // Also check current auth state on initialization
    final currentAuthState = _ref.read(authNotifierProvider);
    _handleAuthStateChange(null, currentAuthState);
  }

  /// Handle authentication state changes
  void _handleAuthStateChange(AuthState? previous, AuthState next) {
    debugPrint('[CartInitializationService] Auth state changed: ${next.runtimeType}');

    next.maybeMap(
      authenticated: (authenticatedState) {
        final user = authenticatedState.user;
        
        // Check if user has backend authentication
        if (user is EnhancedUserModel && user.hasBackendAuth) {
          final userId = user.id;
          
          // Only initialize cart if:
          // 1. We haven't initialized cart yet, OR
          // 2. This is a different user than before
          if (!_hasInitializedCart || _lastAuthenticatedUserId != userId) {
            debugPrint('[CartInitializationService] User authenticated with backend auth, initializing cart...');
            debugPrint('[CartInitializationService] User ID: $userId');
            
            _initializeCartForUser(userId);
            _hasInitializedCart = true;
            _lastAuthenticatedUserId = userId;
          } else {
            debugPrint('[CartInitializationService] Cart already initialized for this user');
          }
        } else {
          debugPrint('[CartInitializationService] User authenticated but no backend auth available');
        }
      },
      unauthenticated: (_) {
        debugPrint('[CartInitializationService] User unauthenticated, resetting cart initialization state');
        _hasInitializedCart = false;
        _lastAuthenticatedUserId = null;
      },
      orElse: () {
        debugPrint('[CartInitializationService] Auth state: ${next.runtimeType}');
      },
    );
  }

  /// Initialize cart for authenticated user
  void _initializeCartForUser(String userId) {
    try {
      debugPrint('[CartInitializationService] Fetching cart for user: $userId');
      
      // Get cart data without forcing loading state (background fetch)
      _ref.read(cartProvider.notifier).getCart(forceRefresh: true);
      
      debugPrint('[CartInitializationService] Cart initialization triggered successfully');
    } catch (e) {
      // Handle errors gracefully - don't block app startup
      debugPrint('[CartInitializationService] Error initializing cart: $e');
      debugPrint('[CartInitializationService] Cart will be loaded when user navigates to cart screen');
    }
  }

  /// Reset initialization state (useful for testing or manual reset)
  void resetInitializationState() {
    debugPrint('[CartInitializationService] Resetting initialization state');
    _hasInitializedCart = false;
    _lastAuthenticatedUserId = null;
  }

  /// Check if cart has been initialized
  bool get hasInitializedCart => _hasInitializedCart;

  /// Get the last authenticated user ID
  String? get lastAuthenticatedUserId => _lastAuthenticatedUserId;
}

/// Provider for cart initialization service
final cartInitializationServiceProvider = Provider<CartInitializationService>((ref) {
  final service = CartInitializationService(ref);
  
  // The service sets up its own listeners in the constructor
  // No need for explicit initialization or disposal
  
  return service;
});
