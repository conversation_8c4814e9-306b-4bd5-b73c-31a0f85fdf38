import 'package:flutter/foundation.dart';
import 'remote_config_service.dart';
import '../enums/payment_method.dart';

/// Service to validate payment configuration before checkout
class PaymentConfigValidator {
  
  /// Validate payment configuration before allowing checkout
  static Future<PaymentValidationResult> validatePaymentConfig({
    required bool isFromBuyNow,
  }) async {
    try {
      debugPrint('[PaymentValidator] 🔍 Validating payment configuration...');
      debugPrint('[PaymentValidator] Is from buy now: $isFromBuyNow');
      
      // Check if Remote Config is properly set up
      await _validateRemoteConfigSetup();
      
      // Get the payment method for this flow
      final PaymentMethod paymentMethod = isFromBuyNow 
          ? RemoteConfigService.getBuyNowPaymentMethod()
          : RemoteConfigService.getCartPaymentMethod();
      
      debugPrint('[PaymentValidator] ✅ Payment method: ${paymentMethod.displayName}');
      
      // Validate the specific payment method
      await _validatePaymentMethod(paymentMethod);
      
      return PaymentValidationResult.success(
        paymentMethod: paymentMethod,
        message: 'Payment configuration is valid',
      );
      
    } catch (e) {
      debugPrint('[PaymentValidator] ❌ Validation failed: $e');
      return PaymentValidationResult.failure(
        error: e.toString(),
        requiresSetup: e.toString().contains('REMOTE CONFIG ERROR'),
      );
    }
  }
  
  /// Check if Remote Config is properly set up
  static Future<void> _validateRemoteConfigSetup() async {
    try {
      // Try to get the configuration - this will throw if not set up
      RemoteConfigService.getCartPaymentMethod();
      RemoteConfigService.getBuyNowPaymentMethod();
      
      debugPrint('[PaymentValidator] ✅ Remote Config is properly configured');
    } catch (e) {
      debugPrint('[PaymentValidator] ❌ Remote Config validation failed: $e');
      throw Exception(
        'Payment configuration not found. Please set up Firebase Remote Config with shop_payment parameter.'
      );
    }
  }
  
  /// Validate specific payment method requirements
  static Future<void> _validatePaymentMethod(PaymentMethod method) async {
    switch (method) {
      case PaymentMethod.stripe:
        await _validateStripeConfig();
        break;
      case PaymentMethod.shopify:
        await _validateShopifyConfig();
        break;
    }
  }
  
  /// Validate Stripe payment configuration
  static Future<void> _validateStripeConfig() async {
    debugPrint('[PaymentValidator] 🔍 Validating Stripe configuration...');
    
    // Add any Stripe-specific validation here
    // For example: check if Stripe keys are configured
    
    debugPrint('[PaymentValidator] ✅ Stripe configuration is valid');
  }
  
  /// Validate Shopify payment configuration
  static Future<void> _validateShopifyConfig() async {
    debugPrint('[PaymentValidator] 🔍 Validating Shopify configuration...');
    
    // Add any Shopify-specific validation here
    // For example: check if Shopify checkout URLs are configured
    
    debugPrint('[PaymentValidator] ✅ Shopify configuration is valid');
  }
  
  /// Quick check if payment config is available (for UI state)
  static bool isPaymentConfigAvailable() {
    try {
      RemoteConfigService.getCartPaymentMethod();
      RemoteConfigService.getBuyNowPaymentMethod();
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Get current payment method safely (for UI display)
  static PaymentMethod? getCurrentPaymentMethodSafe({required bool isFromBuyNow}) {
    try {
      return isFromBuyNow 
          ? RemoteConfigService.getBuyNowPaymentMethod()
          : RemoteConfigService.getCartPaymentMethod();
    } catch (e) {
      debugPrint('[PaymentValidator] ⚠️ Could not get payment method: $e');
      return null;
    }
  }
}

/// Result of payment configuration validation
class PaymentValidationResult {
  final bool isValid;
  final PaymentMethod? paymentMethod;
  final String? error;
  final String? message;
  final bool requiresSetup;
  
  const PaymentValidationResult({
    required this.isValid,
    this.paymentMethod,
    this.error,
    this.message,
    this.requiresSetup = false,
  });
  
  factory PaymentValidationResult.success({
    required PaymentMethod paymentMethod,
    String? message,
  }) {
    return PaymentValidationResult(
      isValid: true,
      paymentMethod: paymentMethod,
      message: message,
    );
  }
  
  factory PaymentValidationResult.failure({
    required String error,
    bool requiresSetup = false,
  }) {
    return PaymentValidationResult(
      isValid: false,
      error: error,
      requiresSetup: requiresSetup,
    );
  }
}
