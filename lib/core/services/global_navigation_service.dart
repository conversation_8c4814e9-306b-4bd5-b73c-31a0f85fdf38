import 'package:flutter/material.dart';
import '../../features/payment/presentation/screens/shopify_checkout_webview.dart';

/// Global navigation service that doesn't depend on widget context
/// Provides navigation capabilities for async operations where context might become unmounted
class GlobalNavigationService {
  static final GlobalNavigationService _instance = GlobalNavigationService._internal();
  factory GlobalNavigationService() => _instance;
  GlobalNavigationService._internal();

  /// Store the current app context for global navigation
  static BuildContext? _globalContext;

  /// Set the global context (called from main app)
  static void setGlobalContext(BuildContext context) {
    // Only log when context actually changes to avoid spam
    if (_globalContext != context) {
      _globalContext = context;
      debugPrint('[GlobalNavigation] Global context set');
    }
  }

  /// Clear the global context
  static void clearGlobalContext() {
    _globalContext = null;
    debugPrint('[GlobalNavigation] Global context cleared');
  }

  /// Check if navigation is available
  bool get canNavigate => _globalContext != null && _globalContext!.mounted;

  /// Get the global context (for internal use)
  static BuildContext? get globalContext => _globalContext;

  /// Navigate to Shopify checkout WebView using global navigation
  /// Returns true if checkout was successful, false if cancelled/failed
  Future<bool> openShopifyCheckout({
    required String checkoutUrl,
    required String orderId,
    required Function(String) onSuccess,
    required Function(String) onError,
    required VoidCallback onCancel,
  }) async {
    if (!canNavigate) {
      debugPrint('[GlobalNavigation] ❌ Navigation not available');
      return false;
    }

    try {
      debugPrint('[GlobalNavigation] 🔗 Opening Shopify checkout: ${checkoutUrl.substring(0, 50)}...');

      // Use root navigator like cart checkout does
      final rootNavigator = Navigator.of(_globalContext!, rootNavigator: true);
      debugPrint('[GlobalNavigation] Using root navigator for navigation');

      final result = await rootNavigator.push<bool>(
        MaterialPageRoute(
          builder: (context) => ShopifyCheckoutWebView(
            checkoutUrl: checkoutUrl,
            orderId: orderId,
            onSuccess: (completedOrderId) {
              debugPrint('[GlobalNavigation] ✅ Checkout successful: $completedOrderId');
              onSuccess(completedOrderId);
              Navigator.of(context).pop(true);
            },
            onError: (error) {
              debugPrint('[GlobalNavigation] ❌ Checkout error: $error');
              onError(error);
              Navigator.of(context).pop(false);
            },
            onCancel: () {
              debugPrint('[GlobalNavigation] ⚠️ Checkout cancelled');
              onCancel();
              Navigator.of(context).pop(false);
            },
            onLoadingEnd: () {
              // WebView handles its own loading states
            },
          ),
        ),
      );

      return result ?? false;
    } catch (e) {
      debugPrint('[GlobalNavigation] ❌ Error opening checkout: $e');
      return false;
    }
  }

  /// Navigate to any route using global navigation
  Future<T?> pushRoute<T>(Route<T> route) async {
    if (!canNavigate) {
      debugPrint('[GlobalNavigation] ❌ Navigation not available for route');
      return null;
    }

    try {
      final rootNavigator = Navigator.of(_globalContext!, rootNavigator: true);
      return await rootNavigator.push<T>(route);
    } catch (e) {
      debugPrint('[GlobalNavigation] ❌ Error pushing route: $e');
      return null;
    }
  }

  /// Pop the current route using global navigation
  void pop<T>([T? result]) {
    if (!canNavigate) {
      debugPrint('[GlobalNavigation] ❌ Navigation not available for pop');
      return;
    }

    try {
      final rootNavigator = Navigator.of(_globalContext!, rootNavigator: true);
      rootNavigator.pop<T>(result);
    } catch (e) {
      debugPrint('[GlobalNavigation] ❌ Error popping route: $e');
    }
  }

  /// Check if we can pop the current route
  bool canPop() {
    if (!canNavigate) return false;
    final rootNavigator = Navigator.of(_globalContext!, rootNavigator: true);
    return rootNavigator.canPop();
  }
}
