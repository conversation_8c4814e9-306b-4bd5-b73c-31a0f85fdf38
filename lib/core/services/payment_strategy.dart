import '../../features/payment/domain/entities/payment_intent.dart';
import '../enums/payment_method.dart';

/// Request data for payment processing
class PaymentRequest {
  final List<Map<String, dynamic>> items;
  final double totalAmount;
  final String currency;
  final bool isFromBuyNow;
  final String? cartId;

  const PaymentRequest({
    required this.items,
    required this.totalAmount,
    required this.currency,
    required this.isFromBuyNow,
    this.cartId,
  });
}

/// Result of payment processing
class PaymentResult {
  final bool isSuccess;
  final String? error;
  final Map<String, dynamic>? data;

  const PaymentResult({
    required this.isSuccess,
    this.error,
    this.data,
  });

  factory PaymentResult.success([Map<String, dynamic>? data]) {
    return PaymentResult(isSuccess: true, data: data);
  }

  factory PaymentResult.failure(String error) {
    return PaymentResult(isSuccess: false, error: error);
  }
}

/// Abstract payment strategy interface
abstract class PaymentStrategy {
  /// Process payment using this strategy
  Future<PaymentResult> processPayment(PaymentRequest request);

  /// Create payment intent using this strategy
  Future<PaymentIntent> createPaymentIntent(PaymentRequest request);

  /// Get the payment method this strategy handles
  PaymentMethod get paymentMethod;
}