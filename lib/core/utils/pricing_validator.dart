import 'package:flutter/foundation.dart';
import 'pricing_logger.dart';

/// Comprehensive validation utility for pricing calculations and discount operations
/// Provides validation methods to prevent negative final amounts and ensure mathematical consistency
class PricingValidator {
  static const String _componentName = 'PricingValidator';

  /// Validate that a pricing calculation is mathematically consistent
  /// Returns true if original - totalDiscount = finalAmount (within tolerance)
  static bool validatePricingConsistency({
    required String context,
    required double originalAmount,
    required double totalDiscount,
    required double finalAmount,
    double tolerance = 0.01,
  }) {
    final expectedFinal = originalAmount - totalDiscount;
    final difference = (finalAmount - expectedFinal).abs();
    final isConsistent = difference <= tolerance;
    
    PricingLogger.logValidation(
      component: _componentName,
      validationType: 'PRICING_CONSISTENCY',
      isValid: isConsistent,
      reason: isConsistent 
        ? 'Calculation is mathematically consistent'
        : 'Final amount does not match expected calculation (diff: ${difference.toStringAsFixed(4)})',
      values: {
        'context': context,
        'original': originalAmount,
        'totalDiscount': totalDiscount,
        'expectedFinal': expectedFinal,
        'actualFinal': finalAmount,
        'difference': difference,
        'tolerance': tolerance,
      },
    );
    
    return isConsistent;
  }

  /// Validate that final amount is not negative
  static bool validateNonNegativeFinal({
    required String context,
    required double finalAmount,
  }) {
    final isValid = finalAmount >= 0;
    
    PricingLogger.logValidation(
      component: _componentName,
      validationType: 'NON_NEGATIVE_FINAL',
      isValid: isValid,
      reason: isValid 
        ? 'Final amount is non-negative'
        : 'Final amount is negative - this should not happen',
      values: {
        'context': context,
        'finalAmount': finalAmount,
      },
    );
    
    return isValid;
  }

  /// Validate that discount amounts are positive (savings should be positive)
  static bool validatePositiveDiscounts({
    required String context,
    required double creditsDiscount,
    required double voucherDiscount,
  }) {
    final creditsValid = creditsDiscount >= 0;
    final voucherValid = voucherDiscount >= 0;
    final allValid = creditsValid && voucherValid;
    
    PricingLogger.logValidation(
      component: _componentName,
      validationType: 'POSITIVE_DISCOUNTS',
      isValid: allValid,
      reason: allValid 
        ? 'All discount amounts are positive'
        : 'Some discount amounts are negative: ${!creditsValid ? 'credits' : ''} ${!voucherValid ? 'voucher' : ''}',
      values: {
        'context': context,
        'creditsDiscount': creditsDiscount,
        'voucherDiscount': voucherDiscount,
        'creditsValid': creditsValid,
        'voucherValid': voucherValid,
      },
    );
    
    return allValid;
  }

  /// Validate that original amount is positive
  static bool validatePositiveOriginal({
    required String context,
    required double originalAmount,
  }) {
    final isValid = originalAmount > 0;
    
    PricingLogger.logValidation(
      component: _componentName,
      validationType: 'POSITIVE_ORIGINAL',
      isValid: isValid,
      reason: isValid 
        ? 'Original amount is positive'
        : 'Original amount is not positive',
      values: {
        'context': context,
        'originalAmount': originalAmount,
      },
    );
    
    return isValid;
  }

  /// Validate that discounts don't exceed original amount
  static bool validateDiscountLimits({
    required String context,
    required double originalAmount,
    required double totalDiscount,
  }) {
    final isValid = totalDiscount <= originalAmount;
    
    PricingLogger.logValidation(
      component: _componentName,
      validationType: 'DISCOUNT_LIMITS',
      isValid: isValid,
      reason: isValid 
        ? 'Total discount does not exceed original amount'
        : 'Total discount exceeds original amount',
      values: {
        'context': context,
        'originalAmount': originalAmount,
        'totalDiscount': totalDiscount,
        'excess': totalDiscount - originalAmount,
      },
    );
    
    return isValid;
  }

  /// Comprehensive validation of a complete pricing calculation
  static ValidationResult validateCompletePricing({
    required String context,
    required double originalAmount,
    required double creditsDiscount,
    required double voucherDiscount,
    required double finalAmount,
    String? voucherCode,
  }) {
    final totalDiscount = creditsDiscount + voucherDiscount;
    
    PricingLogger.logCalculationStep(
      component: _componentName,
      step: 'complete_validation_start',
      description: 'Starting comprehensive pricing validation',
      values: {
        'context': context,
        'originalAmount': originalAmount,
        'creditsDiscount': creditsDiscount,
        'voucherDiscount': voucherDiscount,
        'totalDiscount': totalDiscount,
        'finalAmount': finalAmount,
        'voucherCode': voucherCode,
      },
    );

    final results = <String, bool>{};
    final errors = <String>[];

    // Run all validations
    results['positiveOriginal'] = validatePositiveOriginal(
      context: context,
      originalAmount: originalAmount,
    );
    if (!results['positiveOriginal']!) {
      errors.add('Original amount is not positive');
    }

    results['positiveDiscounts'] = validatePositiveDiscounts(
      context: context,
      creditsDiscount: creditsDiscount,
      voucherDiscount: voucherDiscount,
    );
    if (!results['positiveDiscounts']!) {
      errors.add('Some discount amounts are negative');
    }

    results['discountLimits'] = validateDiscountLimits(
      context: context,
      originalAmount: originalAmount,
      totalDiscount: totalDiscount,
    );
    if (!results['discountLimits']!) {
      errors.add('Total discount exceeds original amount');
    }

    results['nonNegativeFinal'] = validateNonNegativeFinal(
      context: context,
      finalAmount: finalAmount,
    );
    if (!results['nonNegativeFinal']!) {
      errors.add('Final amount is negative');
    }

    results['pricingConsistency'] = validatePricingConsistency(
      context: context,
      originalAmount: originalAmount,
      totalDiscount: totalDiscount,
      finalAmount: finalAmount,
    );
    if (!results['pricingConsistency']!) {
      errors.add('Pricing calculation is not mathematically consistent');
    }

    final isValid = results.values.every((result) => result);

    final validationResult = ValidationResult(
      isValid: isValid,
      errors: errors,
      validationResults: results,
      context: context,
      originalAmount: originalAmount,
      creditsDiscount: creditsDiscount,
      voucherDiscount: voucherDiscount,
      finalAmount: finalAmount,
      voucherCode: voucherCode,
    );

    PricingLogger.logValidation(
      component: _componentName,
      validationType: 'COMPLETE_PRICING_VALIDATION',
      isValid: isValid,
      reason: isValid 
        ? 'All pricing validations passed'
        : 'Some pricing validations failed: ${errors.join(', ')}',
      values: {
        'context': context,
        'validationResults': results,
        'errorCount': errors.length,
        'errors': errors,
      },
    );

    return validationResult;
  }

  /// Test the pricing logger and validator with sample data
  /// This can be called from within the Flutter app to verify logging works
  static void testPricingSystem() {
    debugPrint('🧪 Testing Comprehensive Pricing System...');
    debugPrint('═══════════════════════════════════════════════');

    // Test 1: Valid pricing scenario
    debugPrint('\n✅ Test 1: Valid Pricing Scenario');
    final validResult = validateCompletePricing(
      context: 'TEST_VALID_PRICING',
      originalAmount: 129.99,
      creditsDiscount: 15.00,
      voucherDiscount: 25.99,
      finalAmount: 89.00,
      voucherCode: 'SAVE25',
    );
    debugPrint('Valid Test Result: ${validResult.isValid ? 'PASSED ✅' : 'FAILED ❌'}');

    // Test 2: Error case - negative final amount
    debugPrint('\n❌ Test 2: Error Case - Negative Final Amount');
    final errorResult = validateCompletePricing(
      context: 'TEST_ERROR_NEGATIVE',
      originalAmount: 50.0,
      creditsDiscount: 30.0,
      voucherDiscount: 40.0,
      finalAmount: -20.0,
      voucherCode: 'INVALID',
    );
    debugPrint('Error Test Result: ${errorResult.isValid ? 'UNEXPECTED PASS ⚠️' : 'EXPECTED FAIL ✅'}');
    if (!errorResult.isValid) {
      debugPrint('Expected Errors: ${errorResult.errors}');
    }

    // Test 3: Edge case - zero discounts
    debugPrint('\n🔄 Test 3: Edge Case - Zero Discounts');
    final zeroDiscountResult = validateCompletePricing(
      context: 'TEST_ZERO_DISCOUNTS',
      originalAmount: 99.99,
      creditsDiscount: 0.0,
      voucherDiscount: 0.0,
      finalAmount: 99.99,
      voucherCode: null,
    );
    debugPrint('Zero Discount Test Result: ${zeroDiscountResult.isValid ? 'PASSED ✅' : 'FAILED ❌'}');

    // Test 4: Edge case - maximum discount
    debugPrint('\n💯 Test 4: Edge Case - Maximum Discount');
    final maxDiscountResult = validateCompletePricing(
      context: 'TEST_MAX_DISCOUNT',
      originalAmount: 100.0,
      creditsDiscount: 50.0,
      voucherDiscount: 50.0,
      finalAmount: 0.0,
      voucherCode: 'MAXSAVE',
    );
    debugPrint('Max Discount Test Result: ${maxDiscountResult.isValid ? 'PASSED ✅' : 'FAILED ❌'}');

    // Test 5: Comprehensive logging test
    debugPrint('\n📋 Test 5: Comprehensive Logging Test');
    PricingLogger.logPricingBreakdown(
      component: 'TEST_COMPONENT',
      originalAmount: 129.99,
      creditsDiscount: 15.00,
      voucherDiscount: 25.99,
      finalAmount: 89.00,
      voucherCode: 'SAVE25',
      additionalData: {
        'testMode': true,
        'isFromBuyNow': false,
        'cartId': 'test_cart_123',
      },
    );

    debugPrint('\n✅ Comprehensive Pricing System Test Completed');
    debugPrint('═══════════════════════════════════════════════');
    debugPrint('📊 Summary:');
    debugPrint('   - Valid pricing scenario: ${validResult.isValid ? 'PASSED' : 'FAILED'}');
    debugPrint('   - Error case handling: ${!errorResult.isValid ? 'PASSED' : 'FAILED'}');
    debugPrint('   - Zero discounts: ${zeroDiscountResult.isValid ? 'PASSED' : 'FAILED'}');
    debugPrint('   - Maximum discounts: ${maxDiscountResult.isValid ? 'PASSED' : 'FAILED'}');
    debugPrint('   - Logging system: TESTED');
    debugPrint('\n🎯 Pricing system is working correctly!');
  }
}

/// Result of a comprehensive pricing validation
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final Map<String, bool> validationResults;
  final String context;
  final double originalAmount;
  final double creditsDiscount;
  final double voucherDiscount;
  final double finalAmount;
  final String? voucherCode;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.validationResults,
    required this.context,
    required this.originalAmount,
    required this.creditsDiscount,
    required this.voucherDiscount,
    required this.finalAmount,
    this.voucherCode,
  });

  @override
  String toString() {
    return 'ValidationResult(isValid: $isValid, errors: $errors, context: $context)';
  }
}
