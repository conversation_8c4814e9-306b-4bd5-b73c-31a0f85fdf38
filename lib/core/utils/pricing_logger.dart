import 'package:flutter/foundation.dart';

/// Comprehensive logging utility for pricing operations and discount calculations
/// Provides standardized logging with clear prefixes for easy debugging
class PricingLogger {
  // Cache to prevent duplicate logging of any type
  static final Map<String, String> _lastLoggedContent = {};
  static final Map<String, DateTime> _lastLogTime = {};
  static const int _cacheTimeoutSeconds = 3; // Reduced from 5 to 3 seconds
  static const String _pricingPrefix = '[PRICING]';
  static const String _discountPrefix = '[DISCOUNT]';
  static const String _validationPrefix = '[VALIDATION]';
  static const String _calculationPrefix = '[CALCULATION]';

  /// Generic method to check if we should skip logging based on cache
  static bool _shouldSkipLogging(String logKey, {bool forceLog = false}) {
    if (forceLog) return false;

    final now = DateTime.now();
    final lastLogTime = _lastLogTime[logKey];
    final lastContent = _lastLoggedContent[logKey];

    if (lastLogTime != null && lastContent == logKey) {
      final timeDiff = now.difference(lastLogTime).inSeconds;
      if (timeDiff < _cacheTimeoutSeconds) {
        return true; // Skip logging
      }
    }

    // Update cache
    _lastLoggedContent[logKey] = logKey;
    _lastLogTime[logKey] = now;
    return false; // Don't skip logging
  }

  /// Log a comprehensive pricing breakdown with all calculation steps
  /// Optimized to prevent duplicate logging of the same breakdown
  static void logPricingBreakdown({
    required String component,
    required double originalAmount,
    required double creditsDiscount,
    required double voucherDiscount,
    required double finalAmount,
    String? voucherCode,
    Map<String, dynamic>? additionalData,
    bool forceLog = false, // Force logging even if duplicate
  }) {
    // Create a unique key for this pricing breakdown
    final breakdownKey = '$component-${originalAmount.toStringAsFixed(2)}-${creditsDiscount.toStringAsFixed(2)}-${voucherDiscount.toStringAsFixed(2)}-${finalAmount.toStringAsFixed(2)}-$voucherCode';

    // Check if we've already logged this exact breakdown recently
    final now = DateTime.now();
    final lastLogTime = _lastLogTime[breakdownKey];
    final lastContent = _lastLoggedContent[breakdownKey];

    if (!forceLog && lastLogTime != null && lastContent == breakdownKey) {
      final timeDiff = now.difference(lastLogTime).inSeconds;
      if (timeDiff < _cacheTimeoutSeconds) {
        // Skip logging if same breakdown was logged recently
        return;
      }
    }

    // Update cache
    _lastLoggedContent[breakdownKey] = breakdownKey;
    _lastLogTime[breakdownKey] = now;

    final timestamp = now.toIso8601String();

    debugPrint('$_pricingPrefix [$component] 🧮 PRICING BREAKDOWN - $timestamp');
    debugPrint('$_pricingPrefix [$component] 💰 Original Amount: \$${originalAmount.toStringAsFixed(2)}');
    debugPrint('$_pricingPrefix [$component] 💳 Credits Discount: \$${creditsDiscount.toStringAsFixed(2)}');
    debugPrint('$_pricingPrefix [$component] 🎫 Voucher Discount: \$${voucherDiscount.toStringAsFixed(2)}');
    if (voucherCode != null) {
      debugPrint('$_pricingPrefix [$component] 🏷️ Voucher Code: $voucherCode');
    }
    debugPrint('$_pricingPrefix [$component] 🎯 Final Amount: \$${finalAmount.toStringAsFixed(2)}');

    // Calculate and log total discount
    final totalDiscount = creditsDiscount + voucherDiscount;
    debugPrint('$_pricingPrefix [$component] 📊 Total Discount: \$${totalDiscount.toStringAsFixed(2)}');

    // Verify mathematical consistency
    final expectedFinal = originalAmount - totalDiscount;
    final isConsistent = (finalAmount - expectedFinal).abs() < 0.01; // Allow for floating point precision
    debugPrint('$_pricingPrefix [$component] ✅ Math Check: ${isConsistent ? 'CONSISTENT' : 'INCONSISTENT'}');
    if (!isConsistent) {
      debugPrint('$_pricingPrefix [$component] ⚠️ Expected Final: \$${expectedFinal.toStringAsFixed(2)}, Actual: \$${finalAmount.toStringAsFixed(2)}');
    }

    // Log additional data if provided
    if (additionalData != null && additionalData.isNotEmpty) {
      debugPrint('$_pricingPrefix [$component] 📋 Additional Data: $additionalData');
    }

    debugPrint('$_pricingPrefix [$component] ═══════════════════════════════════════');
  }

  /// Log discount calculation details
  static void logDiscountCalculation({
    required String component,
    required String operation,
    required double amount,
    String? code,
    bool isPositive = true,
    Map<String, dynamic>? details,
  }) {
    final timestamp = DateTime.now().toIso8601String();
    final sign = isPositive ? '+' : '-';
    
    debugPrint('$_discountPrefix [$component] $operation - $timestamp');
    debugPrint('$_discountPrefix [$component] 💰 Amount: $sign\$${amount.abs().toStringAsFixed(2)}');
    if (code != null) {
      debugPrint('$_discountPrefix [$component] 🏷️ Code: $code');
    }
    if (details != null && details.isNotEmpty) {
      debugPrint('$_discountPrefix [$component] 📋 Details: $details');
    }
  }

  /// Log validation results with caching
  static void logValidation({
    required String component,
    required String validationType,
    required bool isValid,
    String? reason,
    Map<String, dynamic>? values,
    bool forceLog = false,
  }) {
    // Create cache key for this validation
    final validationKey = '$component-$validationType-$isValid-$reason-${values?.toString() ?? ''}';

    // Check if we should skip this log
    if (_shouldSkipLogging(validationKey, forceLog: forceLog)) {
      return;
    }

    final timestamp = DateTime.now().toIso8601String();

    debugPrint('$_validationPrefix [$component] $validationType - $timestamp');
    debugPrint('$_validationPrefix [$component] ${isValid ? '✅' : '❌'} Result: ${isValid ? 'VALID' : 'INVALID'}');
    if (reason != null) {
      debugPrint('$_validationPrefix [$component] 📝 Reason: $reason');
    }
    if (values != null && values.isNotEmpty) {
      debugPrint('$_validationPrefix [$component] 📊 Values: $values');
    }
  }

  /// Log calculation steps for debugging with caching
  static void logCalculationStep({
    required String component,
    required String step,
    required String description,
    Map<String, dynamic>? values,
    bool forceLog = false,
  }) {
    // Create cache key for this calculation step
    final stepKey = '$component-$step-$description-${values?.toString() ?? ''}';

    // Check if we should skip this log
    if (_shouldSkipLogging(stepKey, forceLog: forceLog)) {
      return;
    }

    final timestamp = DateTime.now().toIso8601String();

    debugPrint('$_calculationPrefix [$component] Step $step: $description - $timestamp');
    if (values != null && values.isNotEmpty) {
      values.forEach((key, value) {
        if (value is double) {
          debugPrint('$_calculationPrefix [$component]   $key: \$${value.toStringAsFixed(2)}');
        } else {
          debugPrint('$_calculationPrefix [$component]   $key: $value');
        }
      });
    }
  }

  /// Log error conditions with context
  static void logError({
    required String component,
    required String operation,
    required String error,
    Map<String, dynamic>? context,
    StackTrace? stackTrace,
  }) {
    final timestamp = DateTime.now().toIso8601String();
    
    debugPrint('$_pricingPrefix [$component] ❌ ERROR in $operation - $timestamp');
    debugPrint('$_pricingPrefix [$component] 🚨 Error: $error');
    if (context != null && context.isNotEmpty) {
      debugPrint('$_pricingPrefix [$component] 📋 Context: $context');
    }
    if (stackTrace != null) {
      debugPrint('$_pricingPrefix [$component] 📚 Stack Trace: $stackTrace');
    }
  }

  /// Log component initialization with caching
  static void logComponentInit({
    required String component,
    Map<String, dynamic>? initialState,
    bool forceLog = false,
  }) {
    // Create cache key for component initialization
    final initKey = '$component-INIT-${initialState?.toString() ?? ''}';

    // Check if we should skip this log
    if (_shouldSkipLogging(initKey, forceLog: forceLog)) {
      return;
    }

    final timestamp = DateTime.now().toIso8601String();

    debugPrint('$_pricingPrefix [$component] 🚀 INITIALIZED - $timestamp');
    if (initialState != null && initialState.isNotEmpty) {
      debugPrint('$_pricingPrefix [$component] 📋 Initial State: $initialState');
    }
  }

  /// Log display formatting operations with caching
  static void logDisplayFormatting({
    required String component,
    required String displayType,
    required double amount,
    required String formattedValue,
    Map<String, dynamic>? formatOptions,
    bool forceLog = false,
  }) {
    // Create cache key for display formatting
    final formatKey = '$component-$displayType-${amount.toStringAsFixed(2)}-$formattedValue-${formatOptions?.toString() ?? ''}';

    // Check if we should skip this log
    if (_shouldSkipLogging(formatKey, forceLog: forceLog)) {
      return;
    }

    debugPrint('$_pricingPrefix [$component] 🎨 Display $displayType: \$${amount.toStringAsFixed(2)} → $formattedValue');
    if (formatOptions != null && formatOptions.isNotEmpty) {
      debugPrint('$_pricingPrefix [$component] ⚙️ Format Options: $formatOptions');
    }
  }

  /// Validate and log pricing consistency
  static bool validatePricingConsistency({
    required String component,
    required double originalAmount,
    required double totalDiscount,
    required double finalAmount,
    double tolerance = 0.01,
  }) {
    final expectedFinal = originalAmount - totalDiscount;
    final difference = (finalAmount - expectedFinal).abs();
    final isConsistent = difference <= tolerance;
    
    logValidation(
      component: component,
      validationType: 'PRICING_CONSISTENCY',
      isValid: isConsistent,
      reason: isConsistent 
        ? 'Calculation is mathematically consistent'
        : 'Final amount does not match expected calculation',
      values: {
        'original': originalAmount,
        'totalDiscount': totalDiscount,
        'expectedFinal': expectedFinal,
        'actualFinal': finalAmount,
        'difference': difference,
        'tolerance': tolerance,
      },
    );
    
    return isConsistent;
  }

  /// Validate and log that final amount is not negative
  static bool validateNonNegativeFinal({
    required String component,
    required double finalAmount,
  }) {
    final isValid = finalAmount >= 0;

    logValidation(
      component: component,
      validationType: 'NON_NEGATIVE_FINAL',
      isValid: isValid,
      reason: isValid
        ? 'Final amount is non-negative'
        : 'Final amount is negative - this should not happen',
      values: {
        'finalAmount': finalAmount,
      },
    );

    return isValid;
  }

  /// Clear the logging cache to allow fresh logging
  /// Useful when you want to force logging of the same breakdown again
  static void clearCache() {
    _lastLoggedContent.clear();
    _lastLogTime.clear();
    debugPrint('$_pricingPrefix [SYSTEM] 🧹 Logging cache cleared');
  }

  /// Clear cache for a specific component
  static void clearCacheForComponent(String component) {
    final keysToRemove = <String>[];
    for (final key in _lastLoggedContent.keys) {
      if (key.startsWith('$component-')) {
        keysToRemove.add(key);
      }
    }

    for (final key in keysToRemove) {
      _lastLoggedContent.remove(key);
      _lastLogTime.remove(key);
    }

    if (keysToRemove.isNotEmpty) {
      debugPrint('$_pricingPrefix [SYSTEM] 🧹 Cleared cache for component: $component (${keysToRemove.length} entries)');
    }
  }

  /// Force log a pricing breakdown even if it's a duplicate
  static void forceLogPricingBreakdown({
    required String component,
    required double originalAmount,
    required double creditsDiscount,
    required double voucherDiscount,
    required double finalAmount,
    String? voucherCode,
    Map<String, dynamic>? additionalData,
  }) {
    logPricingBreakdown(
      component: component,
      originalAmount: originalAmount,
      creditsDiscount: creditsDiscount,
      voucherDiscount: voucherDiscount,
      finalAmount: finalAmount,
      voucherCode: voucherCode,
      additionalData: additionalData,
      forceLog: true,
    );
  }

  /// Get cache statistics for debugging
  static Map<String, dynamic> getCacheStats() {
    return {
      'totalCachedLogs': _lastLoggedContent.length,
      'oldestCacheEntry': _lastLogTime.values.isEmpty
        ? null
        : _lastLogTime.values.reduce((a, b) => a.isBefore(b) ? a : b).toIso8601String(),
      'newestCacheEntry': _lastLogTime.values.isEmpty
        ? null
        : _lastLogTime.values.reduce((a, b) => a.isAfter(b) ? a : b).toIso8601String(),
    };
  }
}
