// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'TravelGator';

  @override
  String get welcomeMessage => '欢迎使用 TravelGator';

  @override
  String get signIn => '登录';

  @override
  String get signUp => '注册';

  @override
  String get email => '邮箱';

  @override
  String get password => '密码';

  @override
  String get name => '姓名';

  @override
  String get forgotPassword => '忘记密码？';

  @override
  String get orContinueWith => '或继续使用';

  @override
  String get dontHaveAccount => '还没有账户？';

  @override
  String get alreadyHaveAccount => '已有账户？';

  @override
  String get createAccount => '创建账户';

  @override
  String get getStarted => '开始使用';

  @override
  String get legalDocuments => '法律文件';

  @override
  String get userAgreement => '用户协议';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get termsAndConditions => '条款与条件';

  @override
  String lastUpdated(String date) {
    return '最后更新：$date';
  }

  @override
  String get close => '关闭';

  @override
  String get languages => '语言';

  @override
  String get selectLanguage => '选择语言';

  @override
  String get languageDescription => '为应用界面选择您偏好的语言。';

  @override
  String get languageChangeNote => '当您选择新语言时，应用界面将立即更新。';

  @override
  String languageChanged(String language) {
    return '语言已更改为$language';
  }

  @override
  String get accountInformation => '账户信息';

  @override
  String get transactionHistory => '交易记录';

  @override
  String get paymentSettings => '支付设置';

  @override
  String get currency => '货币';

  @override
  String get helpCenter => '帮助中心';

  @override
  String get logout => '退出登录';

  @override
  String get logoutConfirmTitle => '退出登录';

  @override
  String get logoutConfirmMessage => '您确定要退出登录吗？';

  @override
  String get cancel => '取消';

  @override
  String appVersion(String version) {
    return 'TravelGator 版本 $version';
  }

  @override
  String get cart => '购物车';

  @override
  String get cartIsEmpty => '购物车为空';

  @override
  String get addSomeProducts => '添加一些产品开始购物';

  @override
  String get startShopping => '开始购物';

  @override
  String get authenticationRequired => '需要身份验证';

  @override
  String get pleaseSignInToCart => '请登录以访问您的购物车';

  @override
  String get unableToLoadCart => '无法加载购物车';

  @override
  String get failedToLoadCart => '加载购物车失败';

  @override
  String get tryAgain => '重试';

  @override
  String get total => '总计：';

  @override
  String get addToCart => '加入购物车';

  @override
  String get buyNow => '立即购买';

  @override
  String get hideDetails => '隐藏详情';

  @override
  String get remove => '移除';

  @override
  String get itemRemovedFromCart => '商品已从购物车中移除';

  @override
  String get variant => 'Variant';

  @override
  String get noVariantsAvailable => '此产品没有可用的变体';

  @override
  String failedToAddToCart(String error) {
    return '添加到购物车失败：$error';
  }

  @override
  String get gb => 'GB';

  @override
  String get day => '天';

  @override
  String get days => '天';

  @override
  String get signedInSuccessfully => '登录成功！';

  @override
  String signInFailed(String error) {
    return '登录失败：$error';
  }

  @override
  String get retry => '重试';

  @override
  String get orderCompletedSuccessfully => '🎉 订单完成成功！';

  @override
  String checkoutFailed(String error) {
    return '❌ 结账失败：$error';
  }

  @override
  String buyNowFailed(String error) {
    return '❌ 立即购买失败：$error';
  }

  @override
  String get processingCheckout => '正在处理结账...';

  @override
  String get checkOut => '结账';

  @override
  String get proceedToCheckout => '进行结账';

  @override
  String get home => '首页';

  @override
  String get account => '账户';

  @override
  String get sims => 'SIM卡';

  @override
  String get travelgator => 'TravelGator';

  @override
  String get rewards => '奖励';

  @override
  String get travelgatorScreen => 'TravelGator 屏幕';

  @override
  String get welcomeToTravelgator => '欢迎使用\nTravelGator';

  @override
  String get stayConnected => '保持连接';

  @override
  String get stayConnectedGlobally => '全球\n保持连接';

  @override
  String get discoverExplore => '轻松\n发现与探索';

  @override
  String get discoverAmazingDestinations => '发现令人惊叹的\n目的地';

  @override
  String get details => '详情';

  @override
  String get yourPurchase => '您的购买';

  @override
  String get yourData => '您的数据';

  @override
  String get newESim => '新 eSIM';

  @override
  String get topUp => '充值';

  @override
  String get totalPayment => '总付款';

  @override
  String get seeDetails => '查看详情';

  @override
  String get selectYourTravelgatorESim => '选择您的 TravelGator eSIM';

  @override
  String get dataAmount => '数据量';

  @override
  String get duration => '持续时间';

  @override
  String get quantity => '数量';

  @override
  String get eSimType => 'eSIM 类型';

  @override
  String get reviewYourPurchase => '查看您的购买';

  @override
  String get yourDataLabel => '您的数据';

  @override
  String get dayLabel => '天';

  @override
  String get oneDay => '1 天';

  @override
  String get threeDays => '3 天';

  @override
  String get sevenDays => '7 天';

  @override
  String get fifteenDays => '15 天';

  @override
  String get thirtyDays => '30 天';

  @override
  String removeItemConfirmation(String itemTitle) {
    return '您确定要从购物车中移除 \"$itemTitle\" 吗？';
  }

  @override
  String get checkoutFailedTitle => '结账失败';

  @override
  String get checkoutFailedMessage => '处理您的结账时出现问题。这可能是 Shopify 后端配置问题。';

  @override
  String get checkoutErrorTitle => '结账错误';

  @override
  String get deleteMyAccount => '删除我的账户';

  @override
  String get deleteAccountConfirmTitle => '删除账户';

  @override
  String get deleteAccountConfirmMessage => '您确定要删除您的账户吗？这将永久删除您在我们系统中的所有数据。';

  @override
  String get delete => '删除';

  @override
  String get accountDeletionFailed => '账户删除失败';

  @override
  String get accountDeletedSuccessfully => '账户删除成功';

  @override
  String get cannotDeleteAccount => '无法删除账户';

  @override
  String get checkingAccountDeletion => '正在检查是否可以删除账户...';

  @override
  String get deletingAccount => '正在删除账户...';

  @override
  String get serverErrorContactSupport => '服务器错误。请稍后重试或联系客服寻求帮助。';

  @override
  String get errorLoadingProducts => '加载产品时出错';

  @override
  String get noProductsFound => '未找到产品';

  @override
  String get tryAdjustingFilter => '请尝试调整您的筛选条件';

  @override
  String productAddedToCart(String productTitle) {
    return '$productTitle 已添加到购物车！';
  }

  @override
  String get cartOperationMayHaveFailed => '购物车操作可能失败。请检查您的购物车。';

  @override
  String get processingAddToCart => '正在添加到购物车...';

  @override
  String get from => '从';

  @override
  String get minPrice => '最低价格';

  @override
  String get maxPrice => '最高价格';

  @override
  String get applyFilter => '应用筛选';

  @override
  String get clearFilter => '清除筛选';

  @override
  String get priceFilter => '价格筛选';

  @override
  String get under => '低于';

  @override
  String get over => '高于';

  @override
  String get orSetCustomRange => '或设置自定义范围：';

  @override
  String get to => '至';

  @override
  String get countryVietnam => '越南';

  @override
  String get countryChina => '中国';

  @override
  String get countryUnitedStates => '美国';

  @override
  String get countryNewZealand => '新西兰';

  @override
  String get regionAsean => '东盟';

  @override
  String get regionEastAsia => '东亚';

  @override
  String get regionNorthAmerica => '北美';

  @override
  String get regionOceania => '大洋洲';

  @override
  String get eSIMRoamingData => 'eSIM漫游数据';

  @override
  String get countries => '国家';

  @override
  String get regions => '地区';

  @override
  String get authenticationSuccessful => '认证成功！';

  @override
  String get paymentSuccessful => '🎉 Payment successful!';

  @override
  String get paymentCanceled => 'Payment canceled';

  @override
  String get paymentFailed => 'Payment failed';

  @override
  String get paymentErrorUnknown => 'Unknown payment error';

  @override
  String get paymentIntentCreationFailed =>
      'Unable to create payment request. Please try again.';

  @override
  String get paymentVerificationFailed =>
      'Payment verification failed. Please contact support.';

  @override
  String get paymentSucceededOrderFailed =>
      'Payment successful but unable to complete order. Please contact support.';

  @override
  String get backendSystemError =>
      'Backend system error. Please contact technical support.';
}
