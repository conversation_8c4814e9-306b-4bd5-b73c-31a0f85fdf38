// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'TravelGator';

  @override
  String get welcomeMessage => 'Welcome to TravelGator';

  @override
  String get signIn => 'Sign In';

  @override
  String get signUp => 'SIGN UP';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get name => 'Name';

  @override
  String get forgotPassword => 'Forgot Password';

  @override
  String get orContinueWith => 'or continue with';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get createAccount => 'Create Account';

  @override
  String get getStarted => 'Get Started';

  @override
  String get legalDocuments => 'Legal Documents';

  @override
  String get userAgreement => 'User Agreement';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsAndConditions => 'Terms & Conditions';

  @override
  String lastUpdated(String date) {
    return 'Last updated: $date';
  }

  @override
  String get close => 'Close';

  @override
  String get languages => 'Languages';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get languageDescription =>
      'Choose your preferred language for the app interface.';

  @override
  String get languageChangeNote =>
      'The app interface will update immediately when you select a new language.';

  @override
  String languageChanged(String language) {
    return 'Language changed to $language';
  }

  @override
  String get accountInformation => 'Account Information';

  @override
  String get transactionHistory => 'Transaction History';

  @override
  String get paymentSettings => 'Payment Settings';

  @override
  String get currency => 'Currency';

  @override
  String get helpCenter => 'Help Center';

  @override
  String get logout => 'Logout';

  @override
  String get logoutConfirmTitle => 'Logout';

  @override
  String get logoutConfirmMessage => 'Are you sure you want to logout?';

  @override
  String get cancel => 'Cancel';

  @override
  String appVersion(String version) {
    return 'TravelGator version $version';
  }

  @override
  String get cart => 'Cart';

  @override
  String get cartIsEmpty => 'Cart is empty';

  @override
  String get addSomeProducts => 'Add some products to get started';

  @override
  String get startShopping => 'Start Shopping';

  @override
  String get authenticationRequired => 'Authentication Required';

  @override
  String get pleaseSignInToCart => 'Please sign in to access your cart';

  @override
  String get unableToLoadCart => 'Unable to load cart';

  @override
  String get failedToLoadCart => 'Failed to load cart';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get total => 'Total:';

  @override
  String get addToCart => 'Add to Cart';

  @override
  String get buyNow => 'Buy Now';

  @override
  String get hideDetails => 'Hide details';

  @override
  String get remove => 'Remove';

  @override
  String get itemRemovedFromCart => 'Item removed from cart';

  @override
  String get variant => 'Variant';

  @override
  String get noVariantsAvailable => 'No variants available for this product';

  @override
  String failedToAddToCart(String error) {
    return 'Failed to add to cart: $error';
  }

  @override
  String get gb => 'GB';

  @override
  String get day => 'Day';

  @override
  String get days => 'Days';

  @override
  String get signedInSuccessfully => 'Successfully signed in!';

  @override
  String signInFailed(String error) {
    return 'Sign-in failed: $error';
  }

  @override
  String get retry => 'Retry';

  @override
  String get orderCompletedSuccessfully => '🎉 Order completed successfully!';

  @override
  String checkoutFailed(String error) {
    return '❌ Checkout failed: $error';
  }

  @override
  String buyNowFailed(String error) {
    return '❌ Buy Now failed: $error';
  }

  @override
  String get processingCheckout => 'Processing checkout...';

  @override
  String get checkOut => 'Check out';

  @override
  String get proceedToCheckout => 'Proceed to Checkout';

  @override
  String get home => 'Home';

  @override
  String get account => 'Account';

  @override
  String get sims => 'SIMs';

  @override
  String get travelgator => 'TravelGator';

  @override
  String get rewards => 'Rewards';

  @override
  String get travelgatorScreen => 'TravelGator Screen';

  @override
  String get welcomeToTravelgator => 'Welcome to\nTravelGator';

  @override
  String get stayConnected => 'Stay Connected';

  @override
  String get stayConnectedGlobally => 'Stay Connected\nGlobally';

  @override
  String get discoverExplore => 'Discover & Explore\nWith Ease';

  @override
  String get discoverAmazingDestinations => 'Discover Amazing\nDestinations';

  @override
  String get details => 'Details';

  @override
  String get yourPurchase => 'Your Purchase';

  @override
  String get yourData => 'Your Data';

  @override
  String get newESim => 'New eSIM';

  @override
  String get topUp => 'Top Up';

  @override
  String get totalPayment => 'Total Payment';

  @override
  String get seeDetails => 'See details';

  @override
  String get selectYourTravelgatorESim => 'Select your TravelGator eSIM';

  @override
  String get dataAmount => 'Data Amount';

  @override
  String get duration => 'Duration';

  @override
  String get quantity => 'Quantity';

  @override
  String get eSimType => 'eSIM Type';

  @override
  String get reviewYourPurchase => 'Review your purchase';

  @override
  String get yourDataLabel => 'Your data';

  @override
  String get dayLabel => 'Day';

  @override
  String get oneDay => '1 day';

  @override
  String get threeDays => '3 days';

  @override
  String get sevenDays => '7 days';

  @override
  String get fifteenDays => '15 days';

  @override
  String get thirtyDays => '30 days';

  @override
  String removeItemConfirmation(String itemTitle) {
    return 'Are you sure you want to remove \"$itemTitle\" from your cart?';
  }

  @override
  String get checkoutFailedTitle => 'Checkout Failed';

  @override
  String get checkoutFailedMessage =>
      'There was an issue processing your checkout. This is likely a backend configuration issue with Shopify.';

  @override
  String get checkoutErrorTitle => 'Checkout Error';

  @override
  String get deleteMyAccount => 'Delete My Account';

  @override
  String get deleteAccountConfirmTitle => 'Delete Account';

  @override
  String get deleteAccountConfirmMessage =>
      'Are you sure you want to delete your account? This will permanently remove all your data from our system.';

  @override
  String get delete => 'Delete';

  @override
  String get accountDeletionFailed => 'Account deletion failed';

  @override
  String get accountDeletedSuccessfully => 'Account deleted successfully';

  @override
  String get cannotDeleteAccount => 'Cannot delete account';

  @override
  String get checkingAccountDeletion => 'Checking if account can be deleted...';

  @override
  String get deletingAccount => 'Deleting account...';

  @override
  String get serverErrorContactSupport =>
      'Server error occurred. Please try again later or contact support for assistance.';

  @override
  String get errorLoadingProducts => 'Error loading products';

  @override
  String get noProductsFound => 'No products found';

  @override
  String get tryAdjustingFilter => 'Try adjusting your filter';

  @override
  String productAddedToCart(String productTitle) {
    return '$productTitle added to cart!';
  }

  @override
  String get cartOperationMayHaveFailed =>
      'Cart operation may have failed. Please check your cart.';

  @override
  String get processingAddToCart => 'Adding to cart...';

  @override
  String get from => 'From';

  @override
  String get minPrice => 'Min Price';

  @override
  String get maxPrice => 'Max Price';

  @override
  String get applyFilter => 'Apply Filter';

  @override
  String get clearFilter => 'Clear Filter';

  @override
  String get priceFilter => 'Price Filter';

  @override
  String get under => 'Under';

  @override
  String get over => 'Over';

  @override
  String get orSetCustomRange => 'Or set custom range:';

  @override
  String get to => 'to';

  @override
  String get countryVietnam => 'Vietnam';

  @override
  String get countryChina => 'China';

  @override
  String get countryUnitedStates => 'United States';

  @override
  String get countryNewZealand => 'New Zealand';

  @override
  String get regionAsean => 'ASEAN';

  @override
  String get regionEastAsia => 'East Asia';

  @override
  String get regionNorthAmerica => 'North America';

  @override
  String get regionOceania => 'Oceania';

  @override
  String get eSIMRoamingData => 'eSIM Roaming Data';

  @override
  String get countries => 'Countries';

  @override
  String get regions => 'Regions';

  @override
  String get authenticationSuccessful => 'Authentication successful!';

  @override
  String get paymentSuccessful => '🎉 Payment successful!';

  @override
  String get paymentCanceled => 'Payment canceled';

  @override
  String get paymentFailed => 'Payment failed';

  @override
  String get paymentErrorUnknown => 'Unknown payment error';

  @override
  String get paymentIntentCreationFailed =>
      'Unable to create payment request. Please try again.';

  @override
  String get paymentVerificationFailed =>
      'Payment verification failed. Please contact support.';

  @override
  String get paymentSucceededOrderFailed =>
      'Payment successful but unable to complete order. Please contact support.';

  @override
  String get backendSystemError =>
      'Backend system error. Please contact technical support.';
}
