import 'package:dartz/dartz.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import '../entities/cart.dart';
import '../entities/checkout_item.dart';

/// Repository interface for cart operations
abstract class CartRepository {
  /// Get the current cart
  Future<Either<Failure, Cart>> getCart();

  /// Add an item to the cart
  /// If [replaceIfExists] is true, replaces the quantity of existing items instead of incrementing
  /// If [replaceIfExists] is false (default), increments the quantity of existing items
  Future<Either<Failure, Cart>> addItem({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
    bool replaceIfExists = false,
  });

  /// Update an item's quantity in the cart
  Future<Either<Failure, Cart>> updateItem({
    required String variantId,
    required int quantity,
  });

  /// Update an item's complete information in the cart
  Future<Either<Failure, Cart>> updateItemComplete({
    required String oldVariantId,
    required String newVariantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  });

  /// Remove an item from the cart
  Future<Either<Failure, Cart>> removeItem({
    required String variantId,
  });

  /// Proceed to checkout with all items
  Future<Either<Failure, String>> checkout();

  /// Proceed to checkout with selected items only (legacy - full quantities)
  Future<Either<Failure, String>> checkoutSelected({
    required List<String> selectedVariantIds,
  });

  /// Proceed to checkout with specific items and quantities
  Future<Either<Failure, String>> checkoutSelectedWithQuantities({
    required List<CheckoutItem> items,
  });





  /// Update item selection status
  Future<Either<Failure, Cart>> updateItemSelection({
    required String variantId,
    required bool selected,
  });

  /// Update multiple items selection status
  Future<Either<Failure, Cart>> updateMultipleItemsSelection({
    required List<String> variantIds,
    required bool selected,
  });

  /// Select all items in cart
  Future<Either<Failure, Cart>> selectAllItems();

  /// Unselect all items in cart
  Future<Either<Failure, Cart>> unselectAllItems();
}

/// Voucher validation result
class VoucherValidation {
  final bool isValid;
  final double discountAmount;
  final double finalAmount;
  final String? errorMessage;

  const VoucherValidation({
    required this.isValid,
    required this.discountAmount,
    required this.finalAmount,
    this.errorMessage,
  });
}

/// Payment method information
class PaymentMethod {
  final String key;
  final String name;
  final String displayName;
  final bool available;

  const PaymentMethod({
    required this.key,
    required this.name,
    required this.displayName,
    required this.available,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      key: json['key'] as String,
      name: json['name'] as String,
      displayName: json['display_name'] as String,
      available: json['available'] as bool,
    );
  }
}

/// Payment methods response
class PaymentMethodsResponse {
  final List<PaymentMethod> paymentMethods;
  final String? currentPreference;

  const PaymentMethodsResponse({
    required this.paymentMethods,
    this.currentPreference,
  });

  factory PaymentMethodsResponse.fromJson(Map<String, dynamic> json) {
    final methodsData = json['payment_methods'] as List<dynamic>;
    final methods = methodsData.map((m) => PaymentMethod.fromJson(m as Map<String, dynamic>)).toList();

    return PaymentMethodsResponse(
      paymentMethods: methods,
      currentPreference: json['current_preference'] as String?,
    );
  }
}

/// Checkout response
class CheckoutResponse {
  final String paymentMethod;
  final String? clientSecret;
  final String? checkoutUrl;
  final String orderId;
  final bool requiresAction;
  final Map<String, dynamic>? metadata;

  const CheckoutResponse({
    required this.paymentMethod,
    this.clientSecret,
    this.checkoutUrl,
    required this.orderId,
    required this.requiresAction,
    this.metadata,
  });

  factory CheckoutResponse.fromJson(Map<String, dynamic> json) {
    return CheckoutResponse(
      paymentMethod: json['payment_method'] as String,
      clientSecret: json['client_secret'] as String?,
      checkoutUrl: json['checkout_url'] as String?,
      orderId: json['order_id'] as String,
      requiresAction: json['requires_action'] as bool,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}