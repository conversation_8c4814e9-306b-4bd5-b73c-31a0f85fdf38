import 'package:json_annotation/json_annotation.dart';

part 'shopify_discount.g.dart';

/// Shopify discount information
@JsonSerializable()
class ShopifyDiscount {
  final String code;
  final String? id;
  final String? title;
  @JsonKey(name: 'discount_amount')
  final double discountAmount;

  const ShopifyDiscount({
    required this.code,
    this.id,
    this.title,
    required this.discountAmount,
  });

  factory ShopifyDiscount.fromJson(Map<String, dynamic> json) {
    // Handle both 'amount' and 'discount_amount' field names from different APIs
    final modifiedJson = Map<String, dynamic>.from(json);
    if (modifiedJson.containsKey('amount') && !modifiedJson.containsKey('discount_amount')) {
      modifiedJson['discount_amount'] = modifiedJson['amount'];
    }
    return _$ShopifyDiscountFromJson(modifiedJson);
  }

  Map<String, dynamic> toJson() => _$ShopifyDiscountToJson(this);

  ShopifyDiscount copyWith({
    String? code,
    String? id,
    String? title,
    double? discountAmount,
  }) {
    return ShopifyDiscount(
      code: code ?? this.code,
      id: id ?? this.id,
      title: title ?? this.title,
      discountAmount: discountAmount ?? this.discountAmount,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ShopifyDiscount &&
        other.code == code &&
        other.id == id &&
        other.title == title &&
        other.discountAmount == discountAmount;
  }

  @override
  int get hashCode {
    return code.hashCode ^ id.hashCode ^ title.hashCode ^ discountAmount.hashCode;
  }

  @override
  String toString() {
    return 'ShopifyDiscount(code: $code, id: $id, title: $title, discountAmount: $discountAmount)';
  }
}

/// Discount summary information (unified format for both Shopify and legacy)
@JsonSerializable()
class DiscountSummary {
  final String type; // 'shopify' or 'legacy'
  final String code;
  final String? title; // Only available for Shopify discounts
  final double amount;
  final String? id; // Only available for Shopify discounts

  const DiscountSummary({
    required this.type,
    required this.code,
    this.title,
    required this.amount,
    this.id,
  });

  factory DiscountSummary.fromJson(Map<String, dynamic> json) => 
      _$DiscountSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$DiscountSummaryToJson(this);

  DiscountSummary copyWith({
    String? type,
    String? code,
    String? title,
    double? amount,
    String? id,
  }) {
    return DiscountSummary(
      type: type ?? this.type,
      code: code ?? this.code,
      title: title ?? this.title,
      amount: amount ?? this.amount,
      id: id ?? this.id,
    );
  }

  /// Check if this is a Shopify discount
  bool get isShopifyDiscount => type == 'shopify';

  /// Check if this is a legacy discount
  bool get isLegacyDiscount => type == 'legacy';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DiscountSummary &&
        other.type == type &&
        other.code == code &&
        other.title == title &&
        other.amount == amount &&
        other.id == id;
  }

  @override
  int get hashCode {
    return type.hashCode ^ code.hashCode ^ title.hashCode ^ amount.hashCode ^ id.hashCode;
  }

  @override
  String toString() {
    return 'DiscountSummary(type: $type, code: $code, title: $title, amount: $amount, id: $id)';
  }
}
