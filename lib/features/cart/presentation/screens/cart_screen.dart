import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/cart_provider.dart';
import '../providers/cart_state.dart';
import '../widgets/cart_item_card.dart';
import '../widgets/cart_header.dart';
import '../widgets/cart_bottom_info.dart';
import '../widgets/cart_state_widgets.dart';

import '../services/cart_item_service.dart';
import '../services/new_cart_checkout_service.dart';
import '../../../payment/presentation/screens/checkout_screen.dart';
import '../../../payment/presentation/providers/stripe_providers.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';
import '../../../auth/presentation/mixins/auth_redirect_mixin.dart';
import '../../domain/entities/cart.dart';
import '../../../../core/services/remote_config_service.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/enums/payment_method.dart';


class CartScreen extends ConsumerStatefulWidget {
  const CartScreen({super.key});

  @override
  ConsumerState<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends ConsumerState<CartScreen> with AuthRedirectMixin, WidgetsBindingObserver {
  final Set<String> _selectedItems = <String>{};

  bool? _cachedIsAllSelected;
  double? _cachedSelectedTotal;
  String _lastCartHash = '';
  String _lastSelectionHash = '';

  bool _isHandlingCheckout = false;
  bool _isInitialLoad = true;
  String? _lastHandledError;
  bool _isCheckoutLoading = false;

  late CartItemService _itemService;

  @override
  void initState() {
    super.initState();
    _itemService = CartItemService(context, ref, _invalidateCache);

    WidgetsBinding.instance.addObserver(this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthAndLoadCartFast();
    });

    _setupCartErrorListener();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      final cartState = ref.read(cartProvider);
      if (cartState.status == CartStatus.error) {
        debugPrint('[CartScreen] App resumed with cart error, retrying...');
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            ref.read(cartProvider.notifier).getCart();
          }
        });
      }
    }
  }

  void _setupCartErrorListener() {
    ref.listenManual(cartProvider, (previous, next) {
      if (previous?.status == CartStatus.loading &&
          next.status == CartStatus.error &&
          next.error != null) {

        final isConnectionError = next.error!.contains('Connection refused') ||
                                 next.error!.contains('Connection error') ||
                                 next.error!.contains('Backend server is not available') ||
                                 next.error!.contains('check your internet connection') ||
                                 next.error!.contains('Backend authentication required');

        if (isConnectionError && _lastHandledError != next.error && mounted) {
          _lastHandledError = next.error;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text(
                    'Unable to connect to server',
                    style: TextStyle(color: Colors.white),
                  ),
                  backgroundColor: Colors.red[600],
                  duration: const Duration(milliseconds: 800),
                ),
              );

              Future.delayed(const Duration(milliseconds: 900), () {
                if (mounted) {
                  ScaffoldMessenger.of(context).clearSnackBars();

                  Future.delayed(const Duration(milliseconds: 200), () {
                    if (mounted) {
                      context.go('/home');
                    }
                  });
                }
              });
            }
          });
        }
      } else if (next.cart != null) {
        _lastHandledError = null;
      }
    });
  }

  Future<void> _checkAuthAndLoadCartFast() async {
    debugPrint('[CartScreen] Starting fast cart loading for authenticated user...');

    if (mounted) {
      ref.read(cartProvider.notifier).getCart(forceRefresh: true);
    }
  }

  void _initializeSelectedItems(Cart cart) {
    final currentCartHash = cart.items.map((item) => '${item.variantId}:${item.selected}').join(',');
    if (currentCartHash == _lastSelectionHash) {
      debugPrint('[CartScreen] Skipping initialization - cart selection state unchanged');
      return;
    }
    _lastSelectionHash = currentCartHash;

    debugPrint('[CartScreen] Initializing selection for ${cart.items.length} items');
    debugPrint('[CartScreen] Backend selection state: ${cart.items.map((item) => '${item.variantId}:${item.selected}').toList()}');

    final previousSelectedCount = _selectedItems.length;
    setState(() {
      _selectedItems.clear();
      for (final item in cart.items) {
        if (item.selected) {
          _selectedItems.add(item.variantId);
        }
      }
      _invalidateCache();
    });

    debugPrint('[CartScreen] Selection sync: $previousSelectedCount -> ${_selectedItems.length} selected items');
    debugPrint('[CartScreen] Backend selected items: ${cart.items.where((item) => item.selected).map((item) => item.variantId).toList()}');
    debugPrint('[CartScreen] Local selected items: ${_selectedItems.toList()}');

    if (_isInitialLoad) {
      // ✅ FIXED: Always respect backend selection state, never auto-select
      // The backend maintains the user's selection preferences
      debugPrint('[CartScreen] Initial load - respecting existing backend selection state');
      _isInitialLoad = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    setupAuthRedirect();

    final cartState = ref.watch(cartProvider);

    if (cartState.cart != null) {
      _initializeSelectedItems(cartState.cart!);
    }

    if (cartState.cart != null) {
      final currentCartHash = cartState.cart!.items.map((item) => '${item.variantId}:${item.quantity}').join(',');
      if (currentCartHash != _lastCartHash) {
        _invalidateCache();
        _lastCartHash = currentCartHash;
      }
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      appBar: const CartHeader(),
      body: Column(
        children: [
          Expanded(
            child: _buildCartContent(cartState),
          ),

          if (_shouldShowBottomInfo(cartState))
            _buildBottomInfo(cartState.cart!),
        ],
      ),
    );
  }

  bool _shouldShowBottomInfo(CartState cartState) {
    return cartState.status == CartStatus.loaded &&
           cartState.cart != null &&
           cartState.cart!.items.isNotEmpty;
  }

  Widget _buildBottomInfo(Cart cart) {
    final hasSelectedItems = cart.items.any((item) => item.selected);

    return CartBottomInfo(
      isAllSelected: _isAllSelected(cart),
      onSelectAllChanged: _handleSelectAllChanged,
      totalPrice: _calculateSelectedTotal(cart),
      onCheckout: _handleCheckout,
      hasSelectedItems: hasSelectedItems,
      isLoading: _isCheckoutLoading,
      isVoucherLoading: false, // Voucher functionality removed
    );
  }

  void _invalidateCache() {
    _cachedIsAllSelected = null;
    _cachedSelectedTotal = null;
  }

  Widget _buildCartContent(CartState cartState) {
    switch (cartState.status) {
      case CartStatus.initial:
      case CartStatus.loading:
        return const CartLoadingWidget();

      case CartStatus.error:
        final isConnectionError = cartState.error != null && (
          cartState.error!.contains('Connection refused') ||
          cartState.error!.contains('Connection error') ||
          cartState.error!.contains('Backend server is not available') ||
          cartState.error!.contains('check your internet connection') ||
          cartState.error!.contains('Backend authentication required')
        );

        if (isConnectionError) {
          return const CartLoadingWidget();
        }

        return CartErrorWidget(
          error: cartState.error,
          isAuthenticated: cartState.isAuthenticated,
          onRetry: () => ref.read(cartProvider.notifier).getCart(),
          onAuthRequired: _handleAuthRequired,
        );

      case CartStatus.loaded:
        if (cartState.cart == null || cartState.cart!.items.isEmpty) {
          return const CartEmptyWidget();
        }
        return _buildCartItems(cartState.cart!);
    }
  }

  Future<void> _handleAuthRequired() async {
    await AuthGuardService.requireAuthForCart(
      context,
      ref,
      onSuccess: () {
        debugPrint('[CartScreen] Authentication successful - user can now access cart');
        if (mounted) {
          ref.read(cartProvider.notifier).getCart();
        }
      },
    );
  }

  Widget _buildCartItems(Cart cart) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: cart.items.length,
      itemBuilder: (context, index) => _buildCartItemCard(cart.items[index]),
    );
  }

  Widget _buildCartItemCard(item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: CartItemCard(
        item: item,
        isSelected: item.selected,
        onSelectionChanged: (isSelected) => _handleSelectionChanged(item.variantId, isSelected),
        onQuantityChanged: (newQuantity) => _handleQuantityChange(item, newQuantity),
        onRemove: () => _handleRemoveItem(item),
      ),
    );
  }

  void _handleSelectionChanged(String variantId, bool isSelected) {
    debugPrint('🔄 [CartScreen] ===== SELECTION CHANGE START =====');
    debugPrint('🔄 [CartScreen] User action: $variantId -> $isSelected');

    final cartState = ref.read(cartProvider);
    if (cartState.cart != null) {
      final currentItem = cartState.cart!.items.firstWhere(
        (item) => item.variantId == variantId,
        orElse: () => throw Exception('Item not found'),
      );
      debugPrint('🔄 [CartScreen] Current backend state for $variantId: ${currentItem.selected}');

      debugPrint('🔄 [CartScreen] ALL ITEMS CURRENT BACKEND STATE:');
      for (final item in cartState.cart!.items) {
        debugPrint('🔄 [CartScreen] - ${item.variantId}: backend=${item.selected}, local=${_selectedItems.contains(item.variantId)}');
      }
    }

    final previouslySelected = _selectedItems.contains(variantId);
    debugPrint('🔄 [CartScreen] Previous local state: $previouslySelected');

    setState(() {
      if (isSelected) {
        _selectedItems.add(variantId);
      } else {
        _selectedItems.remove(variantId);
      }
      _invalidateCache();
    });

    debugPrint('🔄 [CartScreen] Local state updated: ${_selectedItems.contains(variantId)}');
    debugPrint('🔄 [CartScreen] All local selected items: ${_selectedItems.toList()}');

    debugPrint('🔄 [CartScreen] Sending backend update...');
    ref.read(cartProvider.notifier).updateItemSelection(
      variantId: variantId,
      selected: isSelected,
    ).then((_) {
      debugPrint('🔄 [CartScreen] Backend update completed');

      final newCartState = ref.read(cartProvider);
      if (newCartState.status == CartStatus.error) {
        debugPrint('❌ [CartScreen] Selection update failed, rolling back local state');
        debugPrint('❌ [CartScreen] Error: ${newCartState.error}');

        setState(() {
          if (previouslySelected) {
            _selectedItems.add(variantId);
          } else {
            _selectedItems.remove(variantId);
          }
          _invalidateCache();
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update selection: ${newCartState.error}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else if (newCartState.cart != null) {
        final updatedItem = newCartState.cart!.items.firstWhere(
          (item) => item.variantId == variantId,
          orElse: () => throw Exception('Item not found after update'),
        );
        debugPrint('✅ [CartScreen] Backend state after update: ${updatedItem.selected}');

        if (updatedItem.selected != isSelected) {
          debugPrint('⚠️ [CartScreen] WARNING: Backend state (${updatedItem.selected}) does not match expected state ($isSelected)');
        }
      }

      debugPrint('🔄 [CartScreen] ===== SELECTION CHANGE END =====');
    }).catchError((error) {
      debugPrint('❌ [CartScreen] Backend update threw error: $error');
      debugPrint('🔄 [CartScreen] ===== SELECTION CHANGE END (ERROR) =====');
    });
  }

  bool _isAllSelected(Cart cart) {
    if (cart.items.isEmpty) return false;

    if (_cachedIsAllSelected != null) {
      return _cachedIsAllSelected!;
    }

    _cachedIsAllSelected = cart.items.every((item) => item.selected);
    return _cachedIsAllSelected!;
  }

  void _handleSelectAllChanged(bool selectAll) {
    final cartState = ref.read(cartProvider);
    if (cartState.cart == null) return;

    debugPrint('[CartScreen] Select all changed: $selectAll');

    final previousSelectedItems = Set<String>.from(_selectedItems);

    setState(() {
      if (selectAll) {
        _selectedItems.addAll(cartState.cart!.items.map((item) => item.variantId));
      } else {
        _selectedItems.clear();
      }
      _invalidateCache();
    });

    final Future<void> operation;
    if (selectAll) {
      operation = ref.read(cartProvider.notifier).selectAllItems();
    } else {
      operation = ref.read(cartProvider.notifier).unselectAllItems();
    }

    operation.then((_) {
      final newCartState = ref.read(cartProvider);
      if (newCartState.status == CartStatus.error) {
        debugPrint('[CartScreen] Select all operation failed, rolling back local state');

        setState(() {
          _selectedItems.clear();
          _selectedItems.addAll(previousSelectedItems);
          _invalidateCache();
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update selections: ${newCartState.error}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    });
    debugPrint('[CartScreen] Select all changed: $selectAll, selected count: ${_selectedItems.length}');
  }

  double _calculateSelectedTotal(Cart cart) {
    if (cart.selectedTotalPrice != null) {
      debugPrint('[CartScreen] Using backend selected total: ${cart.selectedTotalPrice}');
      return cart.selectedTotalPrice!;
    }

    if (_cachedSelectedTotal != null) {
      debugPrint('[CartScreen] Using cached selected total: $_cachedSelectedTotal');
      return _cachedSelectedTotal!;
    }

    double total = 0;
    int selectedCount = 0;
    for (final item in cart.items) {
      if (item.selected) {
        total += item.price * item.quantity;
        selectedCount++;
      }
    }

    debugPrint('[CartScreen] Calculated selected total: $total for $selectedCount selected items');
    _cachedSelectedTotal = total;
    return total;
  }

  void _handleQuantityChange(item, int newQuantity) {
    _itemService.handleQuantityChange(item, newQuantity);
  }

  void _handleRemoveItem(item) {
    setState(() {
      _selectedItems.remove(item.variantId);
      _invalidateCache();
    });

    _itemService.showRemoveConfirmationDialog(item);
  }

  void _handleCheckout() async {
    if (_isHandlingCheckout || _isCheckoutLoading) {
      debugPrint('[CartScreen] Already handling checkout, ignoring duplicate call');
      return;
    }

    _isHandlingCheckout = true;
    setState(() {
      _isCheckoutLoading = true;
    });
    debugPrint('[CartScreen] Checkout button pressed - setting guard to true');
    debugPrint('[CartScreen] 🔍 DYNAMIC ROUTING: Starting payment method detection...');

    // Get payment method from remote config to determine routing
    PaymentMethod paymentMethod;
    try {
      debugPrint('[CartScreen] 🔍 DYNAMIC ROUTING: Calling RemoteConfigService.getCartPaymentMethod()...');
      paymentMethod = RemoteConfigService.getCartPaymentMethod();
      debugPrint('[CartScreen] 📋 DYNAMIC ROUTING: Cart payment method detected: ${paymentMethod.displayName}');
      debugPrint('[CartScreen] 📋 DYNAMIC ROUTING: isShopify: ${paymentMethod.isShopify}, isStripe: ${paymentMethod.isStripe}');
    } catch (e) {
      debugPrint('[CartScreen] ❌ DYNAMIC ROUTING: Remote Config failed: $e');

      // Show error and fallback to Stripe behavior
      if (mounted) {
        SnackbarUtils.showError(
          context,
          'Payment setup required. Please contact support.',
          duration: const Duration(seconds: 4),
        );
      }

      // Fallback to Stripe behavior (navigate to CheckoutScreen)
      paymentMethod = PaymentMethod.stripe;
      debugPrint('[CartScreen] 🔄 DYNAMIC ROUTING: Falling back to Stripe payment method');
    }

    final cartState = ref.read(cartProvider);
    if (cartState.cart == null) {
      if (mounted) {
        SnackbarUtils.showError(
          context,
          'Cart is empty',
          duration: const Duration(seconds: 2),
        );
      }
      _isHandlingCheckout = false;
      setState(() {
        _isCheckoutLoading = false;
      });
      return;
    }

    final selectedItems = cartState.cart!.items
        .where((item) => _selectedItems.contains(item.variantId))
        .toList();

    if (selectedItems.isEmpty) {
      if (mounted) {
        SnackbarUtils.showError(
          context,
          'Please select at least one item to checkout',
          duration: const Duration(seconds: 2),
        );
      }
      _isHandlingCheckout = false;
      setState(() {
        _isCheckoutLoading = false;
      });
      return;
    }

    debugPrint('[CartScreen] Checkout: ${selectedItems.length} selected items out of ${cartState.cart!.items.length} total');
    debugPrint('[CartScreen] Selected variants: ${selectedItems.map((item) => item.variantId).toList()}');

    final filteredCart = Cart(
      id: cartState.cart!.id,
      items: selectedItems,
      totalPrice: _calculateSelectedTotal(cartState.cart!),
      currency: cartState.cart!.currency,
      itemsCount: selectedItems.length,
    );

    // Route based on payment method
    debugPrint('[CartScreen] 🔍 DYNAMIC ROUTING: Starting payment method routing...');
    debugPrint('[CartScreen] 🔍 DYNAMIC ROUTING: Payment method: ${paymentMethod.displayName}');
    debugPrint('[CartScreen] 🔍 DYNAMIC ROUTING: isShopify: ${paymentMethod.isShopify}');
    debugPrint('[CartScreen] 🔍 DYNAMIC ROUTING: isStripe: ${paymentMethod.isStripe}');

    try {
      if (paymentMethod.isShopify) {
        // For Shopify: Use NewCartCheckoutService directly (bypass CheckoutScreen)
        debugPrint('[CartScreen] 🛒 DYNAMIC ROUTING: Using Shopify payment flow - bypassing CheckoutScreen');
        await _handleShopifyCheckout(selectedItems);
      } else {
        // For Stripe: Navigate to CheckoutScreen (existing behavior)
        debugPrint('[CartScreen] 💳 DYNAMIC ROUTING: Using Stripe payment flow - navigating to CheckoutScreen');
        await _handleStripeCheckout(filteredCart, selectedItems);
      }
    } catch (e) {
      debugPrint('[CartScreen] ❌ Payment flow failed: $e');
      if (mounted) {
        SnackbarUtils.showError(
          context,
          'Checkout failed: $e',
          duration: const Duration(seconds: 3),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCheckoutLoading = false;
        });
      }
      _isHandlingCheckout = false;
    }
  }

  /// Handle Shopify checkout flow (bypass CheckoutScreen)
  Future<void> _handleShopifyCheckout(List<dynamic> selectedItems) async {
    debugPrint('[CartScreen] 🛒 DYNAMIC ROUTING: Starting Shopify checkout flow');
    debugPrint('[CartScreen] 🛒 DYNAMIC ROUTING: Selected items count: ${selectedItems.length}');

    final selectedVariantIds = selectedItems.map((item) => item.variantId as String).toList();
    debugPrint('[CartScreen] 🛒 DYNAMIC ROUTING: Selected variant IDs: $selectedVariantIds');

    // Use NewCartCheckoutService for direct payment processing
    final newCartCheckoutService = NewCartCheckoutService(
      context: context,
      ref: ref,
      isFromBuyNow: false, // This is cart checkout
    );

    debugPrint('[CartScreen] 🛒 DYNAMIC ROUTING: Calling NewCartCheckoutService.handleSelectedItemsCheckout...');
    await newCartCheckoutService.handleSelectedItemsCheckout(
      selectedVariantIds: selectedVariantIds,
      voucherCode: null,
    );

    // Refresh cart after checkout
    await ref.read(cartProvider.notifier).getCart();
    debugPrint('[CartScreen] ✅ DYNAMIC ROUTING: Shopify checkout completed');
  }

  /// Handle Stripe checkout flow (navigate to CheckoutScreen)
  /// Validates Stripe configuration before navigation
  Future<void> _handleStripeCheckout(Cart filteredCart, List<dynamic> selectedItems) async {
    debugPrint('[CartScreen] 💳 DYNAMIC ROUTING: Starting Stripe checkout flow');
    debugPrint('[CartScreen] 💳 DYNAMIC ROUTING: Selected items count: ${selectedItems.length}');

    if (!mounted) return;

    // Validate Stripe configuration before navigation
    debugPrint('[CartScreen] 🔍 Validating Stripe configuration...');

    final isValid = await ref.read(stripeInitializationProvider.notifier).validateConfig();

    if (!isValid) {
      debugPrint('[CartScreen] ❌ Stripe validation failed - blocking navigation');
      if (mounted) {
        SnackbarUtils.showError(
          context,
          'Payment system is currently unavailable. Please try again later or contact support.',
          duration: const Duration(seconds: 4),
        );
      }
      return;
    }

    debugPrint('[CartScreen] ✅ Stripe validation successful - proceeding to checkout');

    if (mounted) {
      final selectedVariantIds = selectedItems.map((item) => item.variantId as String).toList();

      debugPrint('[CartScreen] 💳 DYNAMIC ROUTING: Passing to CheckoutScreen:');
      debugPrint('[CartScreen] 💳 DYNAMIC ROUTING: Selected variant IDs: $selectedVariantIds');
      debugPrint('[CartScreen] 💳 DYNAMIC ROUTING: Filtered cart items: ${filteredCart.items.length}');
      debugPrint('[CartScreen] 💳 DYNAMIC ROUTING: Filtered cart total: \$${filteredCart.totalPrice.toStringAsFixed(2)}');

      final route = MaterialPageRoute(
        builder: (context) => CheckoutScreen(
          cart: filteredCart,
          isFromBuyNow: false,
          onCloseModal: null,
          selectedVariantIds: selectedVariantIds,
        ),
      );
      debugPrint('[CartScreen] 💳 DYNAMIC ROUTING: Navigating to CheckoutScreen...');
      await Navigator.of(context).push(route);

      // Refresh cart after returning from checkout
      await ref.read(cartProvider.notifier).getCart();
      debugPrint('[CartScreen] ✅ DYNAMIC ROUTING: Returned from CheckoutScreen');
    }
  }

}
