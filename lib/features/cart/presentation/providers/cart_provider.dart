import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import 'package:flutter_travelgator/core/network/api_config.dart';
import 'package:flutter_travelgator/core/utils/shopify_utils.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_providers.dart';
import 'package:flutter_travelgator/features/auth/data/models/enhanced_user_model.dart';
import '../../data/repositories/cart_repository_impl.dart';
import '../../data/services/modern_cart_service.dart';
import '../../domain/entities/cart.dart';
import '../../domain/entities/checkout_item.dart';
import '../../domain/repositories/cart_repository.dart';
import 'cart_state.dart';

/// Provider for cart repository
final cartRepositoryProvider = Provider<CartRepository?>((ref) {
  try {
    final authRepository = ref.watch(authRepositoryProvider);
    if (authRepository == null) {
      return null;
    }
    return CartRepositoryImpl(
      dio: ref.watch(dioProvider),
      authRepository: authRepository,
      ref: ref,
    );
  } catch (e) {
    return null;
  }
});

/// Provider for Dio instance
final dioProvider = Provider<Dio>((ref) {
  return ApiConfig.createDioClient();
});

/// Provider for cart state
final cartProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  final repository = ref.watch(cartRepositoryProvider);

  if (repository == null) {
    return CartNotifier.unauthenticated();
  }

  final notifier = CartNotifier(repository, ref);
  return notifier;
});

/// Notifier for cart operations
class CartNotifier extends StateNotifier<CartState> {
  final CartRepository? _repository;
  final Ref? _ref;
  bool _hasConnectionError = false;

  // Client-side selection state management (fallback when backend doesn't support selection)
  final Map<String, bool> _clientSideSelection = <String, bool>{};

  CartNotifier(this._repository, this._ref) : super(const CartState()) {
    // No automatic cart loading - cart will only be loaded when explicitly requested
    // This prevents unwanted API calls after authentication
  }

  /// Apply client-side selection state to cart items when backend doesn't support selection
  Cart _applyClientSideSelection(Cart cart) {
    // Check if backend supports selection by looking for selection metadata or mixed selection states
    final hasSelectionMetadata = cart.selectedItemsCount != null ||
                                cart.selectedTotalPrice != null ||
                                cart.allItemsSelected != null ||
                                cart.anyItemsSelected != null;

    final hasMixedSelectionStates = cart.items.any((item) => item.selected == false);

    // ✅ FIXED: Also check if backend is explicitly sending selection data
    // If we have items and they have selection state, backend supports selection
    final hasExplicitSelection = cart.items.isNotEmpty;

    final backendSupportsSelection = hasSelectionMetadata || hasMixedSelectionStates || hasExplicitSelection;

    debugPrint('[CartNotifier] Backend selection detection:');
    debugPrint('[CartNotifier] - Has selection metadata: $hasSelectionMetadata');
    debugPrint('[CartNotifier] - Has mixed selection states: $hasMixedSelectionStates');
    debugPrint('[CartNotifier] - Has explicit selection: $hasExplicitSelection');
    debugPrint('[CartNotifier] - Backend supports selection: $backendSupportsSelection');

    if (backendSupportsSelection) {
      debugPrint('[CartNotifier] Backend supports selection, using backend state');
      debugPrint('[CartNotifier] Backend selection state: ${cart.items.map((item) => '${item.variantId}:${item.selected}').toList()}');
      return cart;
    }

    debugPrint('[CartNotifier] Backend does not support selection, applying client-side state');
    debugPrint('[CartNotifier] Client-side selection: $_clientSideSelection');

    // Apply client-side selection state
    final updatedItems = cart.items.map((item) {
      final clientSelected = _clientSideSelection[item.variantId] ?? true; // Default to selected
      return item.copyWith(selected: clientSelected);
    }).toList();

    // Calculate client-side totals
    final selectedItems = updatedItems.where((item) => item.selected).toList();
    final selectedTotal = selectedItems.fold<double>(
      0.0,
      (sum, item) => sum + (item.price * item.quantity)
    );

    return cart.copyWith(
      items: updatedItems,
      selectedItemsCount: selectedItems.length,
      selectedTotalPrice: selectedTotal,
      allItemsSelected: selectedItems.length == updatedItems.length,
      anyItemsSelected: selectedItems.isNotEmpty,
    );
  }

  CartNotifier.unauthenticated() : _repository = null, _ref = null, super(const CartState(
    status: CartStatus.error,
    error: 'Authentication required',
    isAuthenticated: false,
  ));

  /// Update cart state directly with voucher response data
  /// This avoids making an additional API call after voucher application
  void updateCartWithVoucherData(Cart updatedCart) {
    if (_repository == null || _ref == null) {
      debugPrint('[CartNotifier] Cannot update cart - repository or ref is null');
      return;
    }

    debugPrint('[CartNotifier] Updating cart state with voucher data');
    debugPrint('[CartNotifier] Voucher: ${updatedCart.voucherCode}, Discount: \$${updatedCart.discountAmount?.toStringAsFixed(2) ?? '0.00'}');

    // Note: Voucher functionality has been removed from the backend

    // Apply client-side selection state to the updated cart
    final cartWithSelection = _applyClientSideSelection(updatedCart);

    state = state.copyWith(
      status: CartStatus.loaded,
      cart: cartWithSelection,
      clearError: true,
      isAuthenticated: true,
    );

    debugPrint('[CartNotifier] ✅ Cart state updated with voucher data');
  }

  /// Handle connection errors by clearing tokens and showing appropriate error
  Future<void> _handleConnectionError(Failure failure) async {
    if (_ref == null) return;

    // Check if this is a connection error
    final isConnectionError = failure.message.contains('Connection refused') ||
        failure.message.contains('connection error') ||
        failure.message.contains('Network is unreachable') ||
        failure.message.contains('Failed to connect');

    // Check if this is a server error that should trigger sign out (500 errors, database issues)
    final isServerError = failure.message.toLowerCase().contains('server error') ||
        failure.message.contains('500') ||
        failure.message.toLowerCase().contains('no primary_preferred server') ||
        failure.message.toLowerCase().contains('cluster topology=unknown') ||
        failure.message.toLowerCase().contains('mongodb') ||
        failure.message.toLowerCase().contains('database connection');

    if (isConnectionError || isServerError) {
      debugPrint('🔥 [CartNotifier] Connection/Server error detected: ${failure.message}');

      // For server errors (500, database issues), immediately clear tokens
      // For connection errors, only clear tokens if this is a persistent error
      final currentState = state;
      final shouldClearTokens = isServerError ||
          (currentState.status == CartStatus.error &&
           currentState.error != null &&
           currentState.error!.contains('Backend server is not available'));

      if (shouldClearTokens) {
        final errorType = isServerError ? 'Server error' : 'Persistent connection error';
        debugPrint('🧹 [CartNotifier] $errorType - clearing authentication tokens...');
        try {
          await _ref.read(authNotifierProvider.notifier).signOut();
          debugPrint('✅ [CartNotifier] Authentication tokens cleared successfully');
        } catch (e) {
          debugPrint('❌ [CartNotifier] Failed to clear tokens: $e');
        }
      } else {
        debugPrint('🔄 [CartNotifier] First connection attempt failed - keeping tokens for retry');
      }

      // Set error state with user-friendly message
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend server is not available. Please try again later.',
        isAuthenticated: !shouldClearTokens, // Keep auth state if not clearing tokens
        cart: null,
      );
    } else {
      // Handle other types of failures normally
      if (failure is AuthFailure) {
        state = state.copyWith(
          status: CartStatus.error,
          error: failure.message,
          isAuthenticated: false,
        );
      } else {
        state = state.copyWith(
          status: CartStatus.error,
          error: failure.message,
        );
      }
    }
  }

  /// Get the current cart - always fetches fresh data from API
  Future<void> getCart({bool forceRefresh = true}) async {
    debugPrint('[CartNotifier] getCart() called with forceRefresh: $forceRefresh');

    if (_repository == null || _ref == null) {
      debugPrint('[CartNotifier] Repository or ref is null');
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      debugPrint('[CartNotifier] No backend authentication - this should not happen if auth was checked before navigation');
      debugPrint('[CartNotifier] Auth state: ${authState.toString()}');
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if we already know there's a connection error
    if (_hasConnectionError) {
      debugPrint('[CartNotifier] Skipping cart load due to previous connection error');
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Unable to connect to server. The app will continue to work offline.',
        isAuthenticated: true,
      );
      return;
    }

    // Always show loading state for fresh data fetch
    debugPrint('[CartNotifier] Fetching fresh cart data from API (forceRefresh: $forceRefresh)');
    state = state.copyWith(status: CartStatus.loading);

    // Verify token before making API call (this handles token refresh if needed)
    try {
      debugPrint('[CartNotifier] Verifying backend token...');
      final authNotifier = _ref.read(authNotifierProvider.notifier);
      final isTokenValid = await authNotifier.verifyBackendToken();

      if (!isTokenValid) {
        debugPrint('[CartNotifier] Token verification failed');
        state = state.copyWith(
          status: CartStatus.error,
          error: 'Backend authentication required',
          isAuthenticated: false,
        );
        return;
      }
      debugPrint('[CartNotifier] Token verification successful');
      _hasConnectionError = false; // Reset connection error flag on successful verification
    } catch (e) {
      debugPrint('[CartNotifier] Token verification error: $e');

      // Check if this is a connection error that should enable offline mode
      final errorMessage = e.toString().toLowerCase();
      final isConnectionError = errorMessage.contains('connection error') ||
                              errorMessage.contains('connection timeout') ||
                              errorMessage.contains('connection failed') ||
                              errorMessage.contains('network error') ||
                              errorMessage.contains('timeout') ||
                              errorMessage.contains('internet connection');

      if (isConnectionError) {
        debugPrint('[CartNotifier] Connection error during token verification - backend token already cleared by AuthNotifier');
        _hasConnectionError = true; // Mark that we have connection issues

        state = state.copyWith(
          status: CartStatus.error,
          error: 'Unable to connect to server. Please sign in again when connection is restored.',
          isAuthenticated: false, // Set to false since token was cleared
        );
        return;
      }

      // For other errors, treat as auth failure (token already cleared by AuthNotifier if needed)
      debugPrint('[CartNotifier] Auth error during token verification');
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required. Please sign in again.',
        isAuthenticated: false,
      );
      return;
    }

    debugPrint('[CartNotifier] Making API call to get cart...');
    final result = await _repository.getCart();
    result.fold(
      (failure) {
        debugPrint('[CartNotifier] Cart API call failed: ${failure.message}');
        _handleConnectionError(failure);
      },
      (cart) {
        debugPrint('[CartNotifier] Fresh cart data loaded: ${cart.items.length} items');
        // Apply client-side selection state to the loaded cart
        final updatedCart = _applyClientSideSelection(cart);
        state = state.copyWith(
          status: CartStatus.loaded,
          cart: updatedCart,
          clearError: true, // Clear any previous errors
          isAuthenticated: true,
        );

        // Voucher functionality has been removed
        // This initialization is no longer needed
      },
    );
  }

  /// Refresh cart data - always fetches fresh data from API
  Future<void> refreshCart() async {
    debugPrint('[CartNotifier] Refreshing cart data...');
    _hasConnectionError = false; // Reset connection error flag on manual refresh
    await getCart(forceRefresh: true);
  }

  /// Reset connection error flag to allow retry
  void resetConnectionError() {
    debugPrint('[CartNotifier] Resetting connection error flag');
    _hasConnectionError = false;
  }

  /// Synchronize cart items to backend using bulk operations
  /// This method ensures exact quantities are set instead of incrementing existing ones
  /// Used by payment services to prepare backend cart for checkout
  /// Preserves voucher information by re-applying it after cart sync
  Future<bool> syncCartItemsToBackend(Cart cart) async {
    if (_repository == null || _ref == null) {
      debugPrint('[CartNotifier] Cannot sync cart - repository or ref is null');
      return false;
    }

    if (cart.items.isEmpty) {
      debugPrint('[CartNotifier] Cannot sync empty cart');
      return false;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final user = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          if (enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty) {
            return enhancedUser;
          }
        }
        return null;
      },
      orElse: () => null,
    );

    if (user == null) {
      debugPrint('[CartNotifier] Backend authentication required for cart sync');
      return false;
    }

    // Store voucher information before sync to preserve it
    final hasVoucher = cart.hasVoucher == true || (cart.voucherCode != null && cart.voucherCode!.isNotEmpty);
    final voucherCode = cart.voucherCode;

    if (hasVoucher && voucherCode != null) {
      debugPrint('[CartNotifier] 🎫 Cart has voucher: $voucherCode - will preserve after sync');
    }

    debugPrint('[CartNotifier] 🔄 Syncing ${cart.items.length} items to backend cart using bulk operation');

    try {
      // Convert cart items to CartItemUpdate objects for bulk operation
      final cartItemUpdates = cart.items.map((item) => CartItemUpdate(
        variantId: item.variantId,
        productId: item.productId,
        quantity: item.quantity,
        price: item.price,
        title: item.title,
        imageUrl: item.imageUrl,
        selected: item.selected,
      )).toList();

      debugPrint('[CartNotifier] 🔄 Sending bulk update with ${cartItemUpdates.length} items');

      // Use the modern cart service directly for bulk update
      // This sets exact quantities instead of incrementing existing ones
      final dio = _ref.read(dioProvider);
      final modernCartService = ModernCartService(dio: dio);
      final result = await modernCartService.updateMultipleItems(
        items: cartItemUpdates,
        backendToken: user.backendToken!,
      );

      return await result.fold(
        (failure) async {
          debugPrint('[CartNotifier] ❌ Cart sync failed: ${failure.message}');
          _handleConnectionError(failure);
          return false;
        },
        (updatedCart) async {
          debugPrint('[CartNotifier] ✅ Cart sync successful: ${updatedCart.items.length} items in backend');

          // Voucher functionality has been removed
          // Update local state with the synced cart
          final cartWithSelection = _applyClientSideSelection(updatedCart);
          state = state.copyWith(
            status: CartStatus.loaded,
            cart: cartWithSelection,
            clearError: true,
            isAuthenticated: true,
          );
          return true;
        },
      );
    } catch (e) {
      debugPrint('[CartNotifier] ❌ Cart sync error: $e');
      return false;
    }
  }

  /// Add an item to the cart
  /// Returns true if the operation was successful, false otherwise
  /// If [replaceIfExists] is true, replaces the quantity of existing items instead of incrementing
  /// If [replaceIfExists] is false (default), increments the quantity of existing items
  Future<bool> addItem({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
    bool replaceIfExists = false,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return false;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return false;
    }

    // ✅ FIXED: Verify token before making API call (this handles token refresh if needed)
    try {
      debugPrint('[CartNotifier] Verifying backend token before add item...');
      final authNotifier = _ref.read(authNotifierProvider.notifier);
      final isTokenValid = await authNotifier.verifyBackendToken();

      if (!isTokenValid) {
        debugPrint('[CartNotifier] Token verification failed for add item');
        state = state.copyWith(
          status: CartStatus.error,
          error: 'Backend authentication required',
          isAuthenticated: false,
        );
        return false;
      }
      debugPrint('[CartNotifier] Token verification successful for add item');
    } catch (e) {
      debugPrint('[CartNotifier] Token verification error for add item: $e');
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required. Please sign in again.',
        isAuthenticated: false,
      );
      return false;
    }

    // Check if item already exists in cart and increment quantity if it does
    final currentCart = state.cart;
    if (currentCart != null) {
      // Convert GID format to numeric format for comparison
      final numericVariantId = ShopifyUtils.extractVariantId(variantId);
      debugPrint('[CartProvider] Checking for existing item with numeric ID: $numericVariantId (from $variantId)');
      debugPrint('[CartProvider] Current cart has ${currentCart.items.length} items');

      // Find existing item with same variant ID (comparing numeric IDs)
      CartItem? existingItem;
      try {
        existingItem = currentCart.items.firstWhere((item) {
          final itemNumericId = ShopifyUtils.extractVariantId(item.variantId);
          debugPrint('[CartProvider] Comparing $itemNumericId == $numericVariantId');
          return itemNumericId == numericVariantId;
        });
      } catch (e) {
        // Item not found, will add as new item
        debugPrint('[CartProvider] No existing item found, will add as new item');
        existingItem = null;
      }

      if (existingItem != null) {
        // Item exists, either replace or increment quantity based on replaceIfExists parameter
        final newQuantity = replaceIfExists ? quantity : existingItem.quantity + quantity;
        final operation = replaceIfExists ? 'replacing' : 'incrementing';
        debugPrint('[CartProvider] Item $variantId already exists (qty: ${existingItem.quantity}), $operation to $newQuantity');

        // Use updateItem with the existing item's variant ID format
        await updateItem(
          variantId: existingItem.variantId,
          quantity: newQuantity,
        );

        // Check if the update was successful by verifying the cart state
        return state.status != CartStatus.error;
      }
    }

    // Item doesn't exist, add new item
    debugPrint('[CartProvider] Adding new item $variantId with quantity $quantity');
    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.addItem(
      variantId: variantId,
      productId: productId,
      quantity: quantity,
      price: price,
      title: title,
      currency: currency,
      imageUrl: imageUrl,
      replaceIfExists: replaceIfExists,
    );

    return result.fold(
      (failure) {
        _handleConnectionError(failure);
        return false;
      },
      (cart) {
        state = state.copyWith(
          status: CartStatus.loaded,
          cart: cart,
          clearError: true, // Clear any previous errors
          isAuthenticated: true,
        );
        return true;
      },
    );
  }

  /// Update an item's quantity in the cart
  Future<void> updateItem({
    required String variantId,
    required int quantity,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Don't show loading state for quantity updates to avoid refresh feeling
    // state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.updateItem(
      variantId: variantId,
      quantity: quantity,
    );
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Update an item's complete information in the cart
  Future<void> updateItemComplete({
    required String oldVariantId,
    required String newVariantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.updateItemComplete(
      oldVariantId: oldVariantId,
      newVariantId: newVariantId,
      productId: productId,
      quantity: quantity,
      price: price,
      title: title,
      currency: currency,
      imageUrl: imageUrl,
    );
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Remove an item from the cart
  Future<void> removeItem({
    required String variantId,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.removeItem(
      variantId: variantId,
    );
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Proceed to checkout with all items
  Future<String?> checkout() async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.checkout();
    return result.fold(
      (failure) {
        _handleConnectionError(failure);
        return null;
      },
      (successMessage) async {
        // Checkout succeeded, refetch cart to get updated state
        await getCart();
        return successMessage;
      },
    );
  }

  /// Proceed to checkout with selected items only
  Future<String?> checkoutSelected({
    required List<String> selectedVariantIds,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.checkoutSelected(
      selectedVariantIds: selectedVariantIds,
    );
    return result.fold(
      (failure) {
        _handleConnectionError(failure);
        return null;
      },
      (successMessage) async {
        // Checkout succeeded, refetch cart to get updated state
        await getCart();
        return successMessage;
      },
    );
  }

  /// Proceed to checkout with specific items and quantities
  Future<String?> checkoutSelectedWithQuantities({
    required List<CheckoutItem> items,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.checkoutSelectedWithQuantities(
      items: items,
    );
    return result.fold(
      (failure) {
        _handleConnectionError(failure);
        return null;
      },
      (successMessage) async {
        // Checkout succeeded, refetch cart to get updated state
        await getCart();
        return successMessage;
      },
    );
  }

  /// Update item selection status
  Future<void> updateItemSelection({
    required String variantId,
    required bool selected,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    debugPrint('[CartNotifier] Updating item selection: $variantId -> $selected');

    // Update client-side selection state immediately
    _clientSideSelection[variantId] = selected;
    debugPrint('[CartNotifier] Updated client-side selection: $_clientSideSelection');

    // Try to update backend, but don't rely on it for selection state
    final result = await _repository.updateItemSelection(
      variantId: variantId,
      selected: selected,
    );

    result.fold(
      (failure) {
        debugPrint('[CartNotifier] Backend selection update failed, using client-side state');
        // Even if backend fails, we can still use client-side selection
        if (state.cart != null) {
          final updatedCart = _applyClientSideSelection(state.cart!);
          state = state.copyWith(
            status: CartStatus.loaded,
            cart: updatedCart,
            clearError: true,
            isAuthenticated: true,
          );
        }
      },
      (cart) {
        debugPrint('[CartNotifier] Backend responded, applying client-side selection');
        // Apply client-side selection to the cart from backend
        final updatedCart = _applyClientSideSelection(cart);
        state = state.copyWith(
          status: CartStatus.loaded,
          cart: updatedCart,
          clearError: true,
          isAuthenticated: true,
        );
      },
    );
  }

  /// Select all items in cart
  Future<void> selectAllItems() async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    debugPrint('[CartNotifier] Selecting all items');

    // Update client-side selection for all items
    if (state.cart != null) {
      for (final item in state.cart!.items) {
        _clientSideSelection[item.variantId] = true;
      }
      debugPrint('[CartNotifier] Updated client-side selection for all items: $_clientSideSelection');
    }

    final result = await _repository.selectAllItems();
    result.fold(
      (failure) {
        debugPrint('[CartNotifier] Backend select all failed, using client-side state');
        // Even if backend fails, we can still use client-side selection
        if (state.cart != null) {
          final updatedCart = _applyClientSideSelection(state.cart!);
          state = state.copyWith(
            status: CartStatus.loaded,
            cart: updatedCart,
            clearError: true,
            isAuthenticated: true,
          );
        }
      },
      (cart) {
        debugPrint('[CartNotifier] Backend responded to select all, applying client-side selection');
        // Apply client-side selection to the cart from backend
        final updatedCart = _applyClientSideSelection(cart);
        state = state.copyWith(
          status: CartStatus.loaded,
          cart: updatedCart,
          clearError: true,
          isAuthenticated: true,
        );
      },
    );
  }

  /// Bulk buy now operation: prepare cart for buy now checkout
  Future<bool> executeBulkBuyNowOperation({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return false;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return false;
    }

    try {
      debugPrint('[CartProvider] Starting bulk buy now operation for $variantId');
      await getCart(forceRefresh: true);
      if (state.cart == null) {
        throw Exception('Failed to get current cart');
      }

      // Get authenticated user for backend token
      final user = authState.maybeMap(
        authenticated: (state) => state.user as EnhancedUserModel,
        orElse: () => throw Exception('User not authenticated'),
      );

      // Build bulk update: unselect all existing items + add new selected item
      final cartItemUpdates = <CartItemUpdate>[];

      // Add all existing items as unselected
      if (state.cart!.items.isNotEmpty) {
        for (final item in state.cart!.items) {
          cartItemUpdates.add(CartItemUpdate(
            variantId: item.variantId,
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            title: item.title,
            imageUrl: item.imageUrl,
            selected: false, // Unselect all existing items
          ));
        }
      }

      // Add the new buy now item as selected
      cartItemUpdates.add(CartItemUpdate(
        variantId: variantId,
        productId: productId,
        quantity: quantity,
        price: price,
        title: title,
        imageUrl: imageUrl,
        selected: true, // Select the buy now item
      ));

      debugPrint('[CartProvider] Executing bulk update with ${cartItemUpdates.length} items');

      // Execute bulk update using ModernCartService
      final dio = _ref.read(dioProvider);
      final modernCartService = ModernCartService(dio: dio);
      final result = await modernCartService.updateMultipleItems(
        items: cartItemUpdates,
        backendToken: user.backendToken!,
      );

      return result.fold(
        (failure) {
          debugPrint('[CartProvider] ❌ Bulk buy now operation failed: ${failure.message}');
          _handleConnectionError(failure);
          return false;
        },
        (cart) {
          debugPrint('[CartProvider] Bulk buy now operation completed successfully');
          state = state.copyWith(
            status: CartStatus.loaded,
            cart: cart,
            clearError: true,
            isAuthenticated: true,
          );
          return true;
        },
      );
    } catch (e) {
      debugPrint('[CartProvider] ❌ Exception in bulk buy now operation: $e');
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Failed to execute buy now operation: $e',
        isAuthenticated: true,
      );
      return false;
    }
  }



  /// Unselect all items in cart
  Future<void> unselectAllItems() async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // ✅ FIXED: Verify token before making API call (this handles token refresh if needed)
    try {
      debugPrint('[CartNotifier] Verifying backend token before unselect all...');
      final authNotifier = _ref.read(authNotifierProvider.notifier);
      final isTokenValid = await authNotifier.verifyBackendToken();

      if (!isTokenValid) {
        debugPrint('[CartNotifier] Token verification failed for unselect all');
        state = state.copyWith(
          status: CartStatus.error,
          error: 'Backend authentication required',
          isAuthenticated: false,
        );
        return;
      }
      debugPrint('[CartNotifier] Token verification successful for unselect all');
    } catch (e) {
      debugPrint('[CartNotifier] Token verification error for unselect all: $e');
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required. Please sign in again.',
        isAuthenticated: false,
      );
      return;
    }

    debugPrint('[CartNotifier] Unselecting all items');

    // Update client-side selection for all items
    if (state.cart != null) {
      for (final item in state.cart!.items) {
        _clientSideSelection[item.variantId] = false;
      }
      debugPrint('[CartNotifier] Updated client-side selection for all items: $_clientSideSelection');
    }

    final result = await _repository.unselectAllItems();
    result.fold(
      (failure) {
        debugPrint('[CartNotifier] Backend unselect all failed, using client-side state');
        // Even if backend fails, we can still use client-side selection
        if (state.cart != null) {
          final updatedCart = _applyClientSideSelection(state.cart!);
          state = state.copyWith(
            status: CartStatus.loaded,
            cart: updatedCart,
            clearError: true,
            isAuthenticated: true,
          );
        }
      },
      (cart) {
        debugPrint('[CartNotifier] Backend responded to unselect all, applying client-side selection');
        // Apply client-side selection to the cart from backend
        final updatedCart = _applyClientSideSelection(cart);
        state = state.copyWith(
          status: CartStatus.loaded,
          cart: updatedCart,
          clearError: true,
          isAuthenticated: true,
        );
      },
    );
  }
}