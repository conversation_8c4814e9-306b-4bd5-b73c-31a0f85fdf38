import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_travelgator/features/cart/presentation/providers/cart_provider.dart';
import 'package:flutter_travelgator/features/payment/presentation/providers/new_unified_payment_provider.dart';
import 'package:flutter_travelgator/features/payment/presentation/screens/shopify_checkout_webview.dart';
import 'package:flutter_travelgator/core/utils/modal_manager.dart';
import 'package:flutter_travelgator/core/services/global_navigation_service.dart';



/// New cart checkout service implementing the simplified payment flow
/// This replaces the complex draft order flow with:
/// 1. PUT /api/cart (add items) - already handled by cart provider
/// 2. POST /api/vouchers/apply (simple voucher) - new implementation
/// 3. POST /api/orders (universal checkout) - new implementation
/// 4. GET /api/status/:order_id (check status) - existing implementation
class NewCartCheckoutService {
  final BuildContext context;
  final WidgetRef ref;
  final bool isFromBuyNow;

  NewCartCheckoutService({
    required this.context,
    required this.ref,
    this.isFromBuyNow = false,
  });

  /// Handle checkout for all selected items in cart using new simplified flow
  Future<void> handleNewCheckoutFlow({String? voucherCode}) async {
    try {
      debugPrint('[NewCartCheckout] 🚀 Starting new simplified checkout flow');

      // Get current cart state
      final cartState = ref.read(cartProvider);
      if (cartState.cart == null || cartState.cart!.items.isEmpty) {
        _showErrorMessage('Cart is empty');
        return;
      }

      // Show loading indicator
      _showLoadingIndicator();

      // Create payment service with callbacks
      final paymentService = ref.read(newUnifiedPaymentServiceProvider(
        PaymentServiceCallbacks(
          onLoadingStart: () {
            debugPrint('[NewCartCheckout] Payment loading started');
          },
          onLoadingEnd: () {
            debugPrint('[NewCartCheckout] Payment loading ended');
            _hideLoadingIndicator();
          },
          onCloseModal: () {
            debugPrint('[NewCartCheckout] Closing review modal for cart checkout');
            // Close any open modals (like review purchase modal)
            ModalManager().closeAllModals();
          },
          onShowNotification: (message, {bool isError = false}) {
            debugPrint('[NewCartCheckout] Showing notification: $message (error: $isError)');
            // Use post-frame callback for immediate but safe notification display
            WidgetsBinding.instance.addPostFrameCallback((_) {
              // Small delay to ensure navigation animation completes
              Future.delayed(const Duration(milliseconds: 300), () {
                // Use global navigation service for stable context
                final globalContext = GlobalNavigationService.globalContext;
                if (globalContext != null && globalContext.mounted) {
                  debugPrint('[NewCartCheckout] Displaying notification using global context');
                  ScaffoldMessenger.of(globalContext).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          Icon(
                            isError ? Icons.error_outline : Icons.check_circle_outline,
                            color: Colors.white,
                            size: 20,
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              message,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      backgroundColor: isError ? Colors.red : Colors.green,
                      duration: Duration(seconds: isError ? 5 : 4),
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      margin: const EdgeInsets.all(16),
                    ),
                  );
                } else {
                  debugPrint('[NewCartCheckout] Global context not available, notification skipped');
                }
              });
            });
          },
          onNavigateToShopify: (checkoutUrl, orderId) async {
            debugPrint('[NewCartCheckout] 🛒 Navigating to Shopify checkout WebView');
            debugPrint('[NewCartCheckout] 🛒 Checkout URL: ${checkoutUrl.substring(0, 50)}...');
            debugPrint('[NewCartCheckout] 🛒 Order ID: $orderId');

            if (!context.mounted) {
              debugPrint('[NewCartCheckout] ❌ Context not mounted for navigation');
              return false;
            }

            try {
              // Navigate to WebView using the same approach as other services
              final result = await Navigator.of(context).push<bool>(
                MaterialPageRoute(
                  builder: (context) => ShopifyCheckoutWebView(
                    checkoutUrl: checkoutUrl,
                    orderId: orderId,
                    onSuccess: (completedOrderId) {
                      debugPrint('[NewCartCheckout] ✅ Shopify checkout successful: $completedOrderId');
                      if (context.mounted) {
                        Navigator.of(context).pop(true);
                      }
                    },
                    onError: (error) {
                      debugPrint('[NewCartCheckout] ❌ Shopify checkout error: $error');

                      // Show error notification to user using the same system as payment notifications
                      _showErrorNotification(error);

                      if (context.mounted) {
                        Navigator.of(context).pop(false);
                      }
                    },
                    onCancel: () {
                      debugPrint('[NewCartCheckout] ⚠️ Shopify checkout cancelled');
                      if (context.mounted) {
                        Navigator.of(context).pop(false);
                      }
                    },
                    onLoadingEnd: () {
                      // WebView handles its own loading states
                    },
                  ),
                ),
              );

              debugPrint('[NewCartCheckout] 🔄 WebView navigation result: $result');
              return result ?? false;

            } catch (e) {
              debugPrint('[NewCartCheckout] ❌ Shopify WebView navigation failed: $e');
              return false;
            }
          },
        ),
      ));

      // Execute the new unified payment flow
      await paymentService.handlePayment(
        cart: cartState.cart!,
        isFromBuyNow: isFromBuyNow,
      );

      // Navigate to success page
      debugPrint('[NewCartCheckout] ✅ Payment completed successfully');
      if (context.mounted) {
        context.go('/home');
      }

    } catch (e) {
      debugPrint('[NewCartCheckout] ❌ Checkout failed: $e');
      _hideLoadingIndicator();
      if (context.mounted) {
        _showErrorMessage('Checkout failed: $e');
      }
    }
  }

  /// Handle checkout for selected items only using new simplified flow
  Future<void> handleSelectedItemsCheckout({
    required List<String> selectedVariantIds,
    String? voucherCode,
  }) async {
    try {
      debugPrint('[NewCartCheckout] 🚀 Starting selected items checkout with new flow');
      debugPrint('[NewCartCheckout] Selected items: ${selectedVariantIds.length}');

      // Get current cart state
      final cartState = ref.read(cartProvider);
      if (cartState.cart == null || cartState.cart!.items.isEmpty) {
        _showErrorMessage('Cart is empty');
        return;
      }

      // Filter cart to only include selected items
      debugPrint('[NewCartCheckout] 🔍 Filtering cart items...');
      debugPrint('[NewCartCheckout] 🔍 Selected variant IDs: $selectedVariantIds');
      debugPrint('[NewCartCheckout] 🔍 Cart items variant IDs: ${cartState.cart!.items.map((item) => item.variantId).toList()}');

      final selectedItems = cartState.cart!.items
          .where((item) {
            // Handle both numeric and GID format variant IDs
            final itemVariantId = item.variantId;
            return selectedVariantIds.any((selectedId) {
              // Check direct match
              if (selectedId == itemVariantId) return true;

              // Check if selectedId is numeric and itemVariantId is GID format
              if (itemVariantId.startsWith('gid://shopify/ProductVariant/')) {
                final numericPart = itemVariantId.split('/').last;
                if (selectedId == numericPart) return true;
              }

              // Check if selectedId is GID format and itemVariantId is numeric
              if (selectedId.startsWith('gid://shopify/ProductVariant/')) {
                final numericPart = selectedId.split('/').last;
                if (numericPart == itemVariantId) return true;
              }

              return false;
            });
          })
          .toList();

      debugPrint('[NewCartCheckout] 🔍 Filtered items count: ${selectedItems.length}');

      if (selectedItems.isEmpty) {
        debugPrint('[NewCartCheckout] ❌ No items selected for checkout - variant ID mismatch detected');
        _showErrorMessage('No items selected for checkout');
        return;
      }

      // Create a filtered cart with only selected items
      final totalPrice = selectedItems.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
      final itemsCount = selectedItems.fold(0, (sum, item) => sum + item.quantity);

      final filteredCart = cartState.cart!.copyWith(
        items: selectedItems,
        totalPrice: totalPrice,
        itemsCount: itemsCount,
      );

      debugPrint('[NewCartCheckout] Filtered cart: ${filteredCart.items.length} items, total: \$${filteredCart.totalPrice.toStringAsFixed(2)}');

      // Show loading indicator
      _showLoadingIndicator();

      // Create payment service with callbacks
      debugPrint('[NewCartCheckout] 🔧 Creating payment service...');
      final paymentService = ref.read(newUnifiedPaymentServiceProvider(
        PaymentServiceCallbacks(
          onLoadingStart: () {
            debugPrint('[NewCartCheckout] Payment loading started');
          },
          onLoadingEnd: () {
            debugPrint('[NewCartCheckout] Payment loading ended');
            _hideLoadingIndicator();
          },
          onCloseModal: () {
            debugPrint('[NewCartCheckout] Closing review modal for selected items checkout');
            // Close any open modals (like review purchase modal)
            ModalManager().closeAllModals();
          },
          onShowNotification: (message, {bool isError = false}) {
            debugPrint('[NewCartCheckout] Showing notification: $message (error: $isError)');
            // Use post-frame callback for immediate but safe notification display
            WidgetsBinding.instance.addPostFrameCallback((_) {
              // Small delay to ensure navigation animation completes
              Future.delayed(const Duration(milliseconds: 300), () {
                // Use global navigation service for stable context
                final globalContext = GlobalNavigationService.globalContext;
                if (globalContext != null && globalContext.mounted) {
                  debugPrint('[NewCartCheckout] Displaying notification using global context');
                  ScaffoldMessenger.of(globalContext).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          Icon(
                            isError ? Icons.error_outline : Icons.check_circle_outline,
                            color: Colors.white,
                            size: 20,
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              message,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      backgroundColor: isError ? Colors.red : Colors.green,
                      duration: Duration(seconds: isError ? 5 : 4),
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      margin: const EdgeInsets.all(16),
                    ),
                  );
                } else {
                  debugPrint('[NewCartCheckout] Global context not available, notification skipped');
                }
              });
            });
          },
          onNavigateToShopify: (checkoutUrl, orderId) async {
            debugPrint('[NewCartCheckout] 🛒 Navigating to Shopify checkout WebView (selected items)');
            debugPrint('[NewCartCheckout] 🛒 Checkout URL: ${checkoutUrl.substring(0, 50)}...');
            debugPrint('[NewCartCheckout] 🛒 Order ID: $orderId');

            if (!context.mounted) {
              debugPrint('[NewCartCheckout] ❌ Context not mounted for navigation');
              return false;
            }

            try {
              // Navigate to WebView using the same approach as other services
              final result = await Navigator.of(context).push<bool>(
                MaterialPageRoute(
                  builder: (context) => ShopifyCheckoutWebView(
                    checkoutUrl: checkoutUrl,
                    orderId: orderId,
                    onSuccess: (completedOrderId) {
                      debugPrint('[NewCartCheckout] ✅ Shopify checkout successful (selected items): $completedOrderId');
                      if (context.mounted) {
                        Navigator.of(context).pop(true);
                      }
                    },
                    onError: (error) {
                      debugPrint('[NewCartCheckout] ❌ Shopify checkout error (selected items): $error');

                      // Show error notification to user using the same system as payment notifications
                      _showErrorNotification(error);

                      if (context.mounted) {
                        Navigator.of(context).pop(false);
                      }
                    },
                    onCancel: () {
                      debugPrint('[NewCartCheckout] ⚠️ Shopify checkout cancelled (selected items)');
                      if (context.mounted) {
                        Navigator.of(context).pop(false);
                      }
                    },
                    onLoadingEnd: () {
                      // WebView handles its own loading states
                    },
                  ),
                ),
              );

              debugPrint('[NewCartCheckout] 🔄 WebView navigation result (selected items): $result');
              return result ?? false;

            } catch (e) {
              debugPrint('[NewCartCheckout] ❌ Shopify WebView navigation failed (selected items): $e');
              return false;
            }
          },
        ),
      ));

      debugPrint('[NewCartCheckout] 🔧 Payment service created successfully');

      // Execute the new unified payment flow with filtered cart
      debugPrint('[NewCartCheckout] 🚀 Calling paymentService.handlePayment()...');
      await paymentService.handlePayment(
        cart: filteredCart,
        isFromBuyNow: isFromBuyNow,
      );

      debugPrint('[NewCartCheckout] ✅ paymentService.handlePayment() completed');

      // Navigate to success page
      debugPrint('[NewCartCheckout] ✅ Selected items payment completed successfully');
      if (context.mounted) {
        context.go('/home');
      }

    } catch (e) {
      debugPrint('[NewCartCheckout] ❌ Selected items checkout failed: $e');
      _hideLoadingIndicator();
      if (context.mounted) {
        _showErrorMessage('Checkout failed: $e');
      }
    }
  }

  /// Show loading indicator
  void _showLoadingIndicator() {
    if (context.mounted) {
      // Use a simple CircularProgressIndicator for now
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
  }

  /// Hide loading indicator
  void _hideLoadingIndicator() {
    if (context.mounted && Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  /// Show error notification using the same system as payment notifications
  void _showErrorNotification(String message) {
    debugPrint('[NewCartCheckout] Showing error notification: $message');
    // Use post-frame callback for immediate but safe notification display
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Small delay to ensure navigation animation completes
      Future.delayed(const Duration(milliseconds: 300), () {
        // Use global navigation service for stable context
        final globalContext = GlobalNavigationService.globalContext;
        if (globalContext != null && globalContext.mounted) {
          debugPrint('[NewCartCheckout] Displaying error notification using global context');
          ScaffoldMessenger.of(globalContext).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.white,
                    size: 20,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      message,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 5),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );
        } else {
          debugPrint('[NewCartCheckout] Global context not available, error notification skipped');
        }
      });
    });
  }

  /// Show error message
  void _showErrorMessage(String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}

/// Provider for NewCartCheckoutService
/// Note: This should be used with a WidgetRef, not ProviderRef
/// Usage: ref.read(newCartCheckoutServiceProvider(context))
final newCartCheckoutServiceProvider = Provider.family<NewCartCheckoutService, ({BuildContext context, WidgetRef ref})>((providerRef, params) {
  return NewCartCheckoutService(
    context: params.context,
    ref: params.ref,
  );
});
