import 'dart:convert';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import 'package:flutter_travelgator/core/utils/shopify_utils.dart';
import 'package:flutter_travelgator/features/auth/domain/repositories/auth_repository.dart';
import 'package:flutter_travelgator/features/auth/data/models/enhanced_user_model.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_providers.dart';

import '../../domain/entities/cart.dart';
import '../../domain/entities/checkout_item.dart';
import '../../domain/repositories/cart_repository.dart';
import '../services/modern_cart_service.dart';

/// Implementation of the cart repository
class CartRepositoryImpl implements CartRepository {
  final Dio _dio;
  final Ref _ref;
  final ModernCartService _modernCartService;

  CartRepositoryImpl({
    required Dio dio,
    required AuthRepository authRepository,
    required Ref ref,
  })  : _dio = dio,
        _ref = ref,
        _modernCartService = ModernCartService(dio: dio);

  /// Get the current authenticated user from auth state
  EnhancedUserModel? _getCurrentAuthenticatedUser() {
    final authState = _ref.read(authNotifierProvider);
    return authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          return state.user as EnhancedUserModel;
        }
        return null;
      },
      orElse: () => null,
    );
  }

  /// Convert CartItem entity to CartItemUpdate model for bulk operations
  CartItemUpdate _cartItemToCartItemUpdate(CartItem item) {
    return CartItemUpdate(
      variantId: item.variantId,
      productId: item.productId,
      quantity: item.quantity,
      price: item.price,
      title: item.title,
      imageUrl: item.imageUrl,
      selected: item.selected,
    );
  }





  @override
  Future<Either<Failure, Cart>> getCart() async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;
      debugPrint('[CartRepository] Using backend token from EnhancedUserModel: ${backendToken.substring(0, 20)}...');

      final response = await _dio.get(
        '/cart',
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
          },
        ),
      );

      // Parse the cart object from the response data
      final cartData = response.data['data'] as Map<String, dynamic>;
      return Right(Cart.fromJson(cartData));
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> addItem({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
    bool replaceIfExists = false,
  }) async {
    // Get current authenticated user from auth state
    final user = _getCurrentAuthenticatedUser();

    if (user == null) {
      debugPrint('[CartRepository] No authenticated user found');
      return const Left(AuthFailure(message: 'Authentication required'));
    }

    if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
      debugPrint('[CartRepository] User does not have backend authentication');
      return const Left(AuthFailure(message: 'Backend authentication required'));
    }

    final backendToken = user.backendToken!;
    debugPrint('[CartRepository] ✅ BULK ADD: Using fetch-current-cart-then-send-full-cart pattern');

    // Extract numeric IDs from GID format for backend
    final numericVariantId = ShopifyUtils.extractVariantId(variantId);
    final numericProductId = ShopifyUtils.extractProductId(productId);

    debugPrint('[CartRepository] Converting GID to numeric ID:');
    debugPrint('[CartRepository] Variant: $variantId -> $numericVariantId');
    debugPrint('[CartRepository] Product: $productId -> $numericProductId');

    try {
      // Step 1: Get current cart state
      debugPrint('[CartRepository] ✅ BULK ADD: Step 1 - Fetching current cart state');
      final currentCartResult = await getCart();

      return await currentCartResult.fold(
        (failure) {
          debugPrint('[CartRepository] ✅ BULK ADD: Failed to get current cart, falling back to individual add');
          // Fallback to individual operation if cart fetch fails
          return _modernCartService.addItem(
            variantId: numericVariantId,
            productId: numericProductId,
            quantity: quantity,
            price: price,
            title: title,
            imageUrl: imageUrl,
            backendToken: backendToken,
          );
        },
        (currentCart) async {
          debugPrint('[CartRepository] ✅ BULK ADD: Step 2 - Applying add operation to local cart state');

          // Step 2: Apply the add operation to local cart state
          final updatedItems = List<CartItem>.from(currentCart.items);

          // Check if item already exists in cart
          final existingItemIndex = updatedItems.indexWhere((item) => item.variantId == numericVariantId);

          if (existingItemIndex != -1) {
            // Update existing item quantity
            final existingItem = updatedItems[existingItemIndex];
            final newQuantity = replaceIfExists ? quantity : existingItem.quantity + quantity;
            updatedItems[existingItemIndex] = CartItem(
              variantId: existingItem.variantId,
              productId: existingItem.productId,
              quantity: newQuantity,
              price: price, // Use new price
              title: title, // Use new title
              imageUrl: imageUrl ?? existingItem.imageUrl,
              selected: existingItem.selected,
            );
            final operation = replaceIfExists ? 'replaced' : 'incremented';
            debugPrint('[CartRepository] ✅ BULK ADD: $operation existing item quantity: ${existingItem.quantity} -> $newQuantity');
          } else {
            // Add new item
            updatedItems.add(CartItem(
              variantId: numericVariantId,
              productId: numericProductId,
              quantity: quantity,
              price: price,
              title: title,
              imageUrl: imageUrl,
              selected: true, // New items are selected by default
            ));
            debugPrint('[CartRepository] ✅ BULK ADD: Added new item to cart');
          }

          // Step 3: Convert all items to CartItemUpdate and send via bulk operation
          debugPrint('[CartRepository] ✅ BULK ADD: Step 3 - Sending all ${updatedItems.length} items via bulk operation');
          final cartItemUpdates = updatedItems.map(_cartItemToCartItemUpdate).toList();

          return await _modernCartService.updateMultipleItems(
            items: cartItemUpdates,
            backendToken: backendToken,
          );
        },
      );
    } catch (e) {
      debugPrint('[CartRepository] ✅ BULK ADD: Error in bulk operation: $e');
      return Left(UnknownFailure(message: 'Failed to add item: $e'));
    }
  }

  @override
  Future<Either<Failure, Cart>> updateItem({
    required String variantId,
    required int quantity,
  }) async {
    // Get current authenticated user from auth state
    final user = _getCurrentAuthenticatedUser();

    if (user == null) {
      debugPrint('[CartRepository] No authenticated user found');
      return const Left(AuthFailure(message: 'Authentication required'));
    }

    if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
      debugPrint('[CartRepository] User does not have backend authentication');
      return const Left(AuthFailure(message: 'Backend authentication required'));
    }

    final backendToken = user.backendToken!;
    debugPrint('[CartRepository] ✅ BULK UPDATE: Using fetch-current-cart-then-send-full-cart pattern');
    debugPrint('[CartRepository] Updating item with variant ID: $variantId to quantity: $quantity');

    try {
      // Step 1: Get current cart state
      debugPrint('[CartRepository] ✅ BULK UPDATE: Step 1 - Fetching current cart state');
      final currentCartResult = await getCart();

      return await currentCartResult.fold(
        (failure) {
          debugPrint('[CartRepository] ✅ BULK UPDATE: Failed to get current cart: ${failure.message}');
          return Left(failure);
        },
        (currentCart) async {
          debugPrint('[CartRepository] ✅ BULK UPDATE: Step 2 - Applying update operation to local cart state');

          // Step 2: Apply the update operation to local cart state
          final updatedItems = <CartItem>[];
          bool itemFound = false;

          for (final item in currentCart.items) {
            if (item.variantId == variantId) {
              // Update the quantity for this item
              updatedItems.add(CartItem(
                variantId: item.variantId,
                productId: item.productId,
                quantity: quantity,
                price: item.price,
                title: item.title,
                imageUrl: item.imageUrl,
                selected: item.selected,
              ));
              itemFound = true;
              debugPrint('[CartRepository] ✅ BULK UPDATE: Updated item $variantId quantity: ${item.quantity} -> $quantity');
            } else {
              // Keep other items unchanged
              updatedItems.add(item);
            }
          }

          if (!itemFound) {
            debugPrint('[CartRepository] ✅ BULK UPDATE: Item $variantId not found in cart');
            return const Left(ServerFailure(message: 'Item not found in cart'));
          }

          // Step 3: Convert all items to CartItemUpdate and send via bulk operation
          debugPrint('[CartRepository] ✅ BULK UPDATE: Step 3 - Sending all ${updatedItems.length} items via bulk operation');
          final cartItemUpdates = updatedItems.map(_cartItemToCartItemUpdate).toList();

          return await _modernCartService.updateMultipleItems(
            items: cartItemUpdates,
            backendToken: backendToken,
          );
        },
      );
    } catch (e) {
      debugPrint('[CartRepository] ✅ BULK UPDATE: Error in bulk operation: $e');
      return Left(UnknownFailure(message: 'Failed to update item: $e'));
    }
  }

  @override
  Future<Either<Failure, Cart>> updateItemComplete({
    required String oldVariantId,
    required String newVariantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  }) async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      // Convert GID format to numeric format for backend compatibility
      final numericOldVariantId = ShopifyUtils.extractNumericId(oldVariantId);
      final numericNewVariantId = ShopifyUtils.extractNumericId(newVariantId);
      final numericProductId = ShopifyUtils.extractNumericId(productId);

      debugPrint('[CartRepository] Updating item complete - Old variant: $oldVariantId -> New variant: $newVariantId');

      final response = await _dio.patch(
        '/cart/update_item_complete',
        data: {
          'old_variant_id': numericOldVariantId,
          'new_variant_id': numericNewVariantId,
          'product_id': numericProductId,
          'quantity': quantity,
          'price': price,
          'title': title,
          'currency': currency,
          if (imageUrl != null) 'image_url': imageUrl,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Parse the cart object from the response data
      final cartData = response.data['data'] as Map<String, dynamic>;
      return Right(Cart.fromJson(cartData));
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> removeItem({
    required String variantId,
  }) async {
    // Get current authenticated user from auth state
    final user = _getCurrentAuthenticatedUser();

    if (user == null) {
      debugPrint('[CartRepository] No authenticated user found');
      return const Left(AuthFailure(message: 'Authentication required'));
    }

    if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
      debugPrint('[CartRepository] User does not have backend authentication');
      return const Left(AuthFailure(message: 'Backend authentication required'));
    }

    final backendToken = user.backendToken!;
    debugPrint('[CartRepository] ✅ BULK REMOVE: Using fetch-current-cart-then-send-full-cart pattern');
    debugPrint('[CartRepository] Removing item with variant ID: $variantId');

    try {
      // Step 1: Get current cart state
      debugPrint('[CartRepository] ✅ BULK REMOVE: Step 1 - Fetching current cart state');
      final currentCartResult = await getCart();

      return await currentCartResult.fold(
        (failure) {
          debugPrint('[CartRepository] ✅ BULK REMOVE: Failed to get current cart, falling back to individual remove');
          // Fallback to individual operation if cart fetch fails
          return _modernCartService.removeItem(
            variantId: variantId,
            backendToken: backendToken,
          );
        },
        (currentCart) async {
          debugPrint('[CartRepository] ✅ BULK REMOVE: Step 2 - Applying remove operation to local cart state');

          // Step 2: Apply the remove operation to local cart state
          final updatedItems = currentCart.items.where((item) => item.variantId != variantId).toList();

          final removedCount = currentCart.items.length - updatedItems.length;
          debugPrint('[CartRepository] ✅ BULK REMOVE: Removed $removedCount item(s), ${updatedItems.length} items remaining');

          if (removedCount == 0) {
            debugPrint('[CartRepository] ✅ BULK REMOVE: Item $variantId not found in cart');
            return const Left(ServerFailure(message: 'Item not found in cart'));
          }

          // Step 3: Convert all remaining items to CartItemUpdate and send via bulk operation
          debugPrint('[CartRepository] ✅ BULK REMOVE: Step 3 - Sending remaining ${updatedItems.length} items via bulk operation');
          final cartItemUpdates = updatedItems.map(_cartItemToCartItemUpdate).toList();

          return await _modernCartService.updateMultipleItems(
            items: cartItemUpdates,
            backendToken: backendToken,
          );
        },
      );
    } catch (e) {
      debugPrint('[CartRepository] ✅ BULK REMOVE: Error in bulk operation: $e');
      return Left(UnknownFailure(message: 'Failed to remove item: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> checkout() async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      debugPrint('[CartRepository] Checkout all items using unified endpoint');

      // Use unified checkout endpoint with empty body (Priority 5: checkout all items)
      final response = await _dio.post(
        '/cart/checkout',
        data: {}, // Empty body triggers "checkout all items" logic
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Handle response format - check if data exists and has checkout_url
      final data = response.data['data'] as Map<String, dynamic>?;
      final checkoutUrl = data?['checkout_url'] as String?;

      if (data == null || checkoutUrl == null || checkoutUrl.isEmpty) {
        // Checkout failed, no valid checkout URL
        final message = data?['message'] as String? ?? 'Checkout failed - no checkout URL received';
        return Left(ServerFailure(message: message));
      }

      // Checkout succeeded, return full JSON response for frontend processing
      // Create a success response format that the frontend expects
      final successResponse = {
        'success': true,
        'data': data,
        'message': data['message'] ?? 'Checkout URL created successfully'
      };
      return Right(json.encode(successResponse));
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> checkoutSelected({
    required List<String> selectedVariantIds,
  }) async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      debugPrint('[CartRepository] Checkout selected variants using unified endpoint: ${selectedVariantIds.join(', ')}');

      // Use unified checkout endpoint with selected_variant_ids (Priority 2)
      final response = await _dio.post(
        '/cart/checkout',
        data: {
          'selected_variant_ids': selectedVariantIds,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Handle response format - check if data exists and has checkout_url
      final data = response.data['data'] as Map<String, dynamic>?;
      final checkoutUrl = data?['checkout_url'] as String?;

      if (data == null || checkoutUrl == null || checkoutUrl.isEmpty) {
        // Checkout failed, no valid checkout URL
        final message = data?['message'] as String? ?? 'Checkout failed - no checkout URL received';
        return Left(ServerFailure(message: message));
      }

      // Checkout succeeded, return full JSON response for frontend processing
      // Create a success response format that the frontend expects
      final successResponse = {
        'success': true,
        'data': data,
        'message': data['message'] ?? 'Checkout URL created successfully'
      };

      return Right(json.encode(successResponse));
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> checkoutSelectedWithQuantities({
    required List<CheckoutItem> items,
  }) async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      debugPrint('[CartRepository] Checkout selected items with custom quantities using unified endpoint: ${items.map((item) => '${item.variantId}:${item.quantity}').join(', ')}');

      // Use unified checkout endpoint with items array (Priority 1: highest priority)
      final response = await _dio.post(
        '/cart/checkout',
        data: {
          'items': items.map((item) => item.toJson()).toList(),
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Handle response format - check if data exists and has checkout_url
      final data = response.data['data'] as Map<String, dynamic>?;
      final checkoutUrl = data?['checkout_url'] as String?;

      if (data == null || checkoutUrl == null || checkoutUrl.isEmpty) {
        // Checkout failed, no valid checkout URL
        final message = data?['message'] as String? ?? 'Checkout failed - no checkout URL received';
        return Left(ServerFailure(message: message));
      }

      // Checkout succeeded, return full JSON response for frontend processing
      // Create a success response format that the frontend expects
      final successResponse = {
        'success': true,
        'data': data,
        'message': data['message'] ?? 'Checkout URL created successfully'
      };

      return Right(json.encode(successResponse));
    } on DioException catch (e) {
      debugPrint('[CartRepository] DioException during checkout: ${e.message}');
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      debugPrint('[CartRepository] Exception during checkout: $e');
      return Left(UnknownFailure(message: e.toString()));
    }
  }









  @override
  Future<Either<Failure, Cart>> updateItemSelection({
    required String variantId,
    required bool selected,
  }) async {
    // Get current authenticated user from auth state
    final user = _getCurrentAuthenticatedUser();

    if (user == null) {
      debugPrint('[CartRepository] No authenticated user found');
      return const Left(AuthFailure(message: 'Authentication required'));
    }

    if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
      debugPrint('[CartRepository] User does not have backend authentication');
      return const Left(AuthFailure(message: 'Backend authentication required'));
    }

    final backendToken = user.backendToken!;
    debugPrint('[CartRepository] ✅ BULK SELECTION: Using fetch-current-cart-then-send-full-cart pattern');
    debugPrint('[CartRepository] Updating selection for variant ID: $variantId to selected: $selected');

    try {
      // Step 1: Get current cart state
      debugPrint('[CartRepository] ✅ BULK SELECTION: Step 1 - Fetching current cart state');
      final currentCartResult = await getCart();

      return await currentCartResult.fold(
        (failure) {
          debugPrint('[CartRepository] ✅ BULK SELECTION: Failed to get current cart: ${failure.message}');
          return Left(failure);
        },
        (currentCart) async {
          debugPrint('[CartRepository] ✅ BULK SELECTION: Step 2 - Applying selection update to local cart state');
          debugPrint('[CartRepository] Total items in cart: ${currentCart.items.length}');

          // Step 3: Create updated cart items with the new selection state
          final updatedItems = currentCart.items.map((item) {
            final isSelected = item.variantId == variantId ? selected : item.selected;
            debugPrint('[CartRepository] Item ${item.variantId}: selected = $isSelected');

            return CartItemUpdate(
              variantId: item.variantId,
              productId: item.productId,
              quantity: item.quantity,
              price: item.price,
              title: item.title,
              imageUrl: item.imageUrl,
              selected: isSelected,
            );
          }).toList();

          // Step 4: Send entire updated cart in ONE PUT call
          debugPrint('[CartRepository] ✅ BULK SELECTION: Step 3 - Sending all ${updatedItems.length} items via bulk operation');
          return await _modernCartService.updateMultipleItems(
            items: updatedItems,
            backendToken: backendToken,
          );
        },
      );
    } catch (e) {
      debugPrint('[CartRepository] ✅ BULK SELECTION: Error in bulk operation: $e');
      return Left(UnknownFailure(message: 'Failed to update item selection: $e'));
    }
  }

  @override
  Future<Either<Failure, Cart>> updateMultipleItemsSelection({
    required List<String> variantIds,
    required bool selected,
  }) async {
    // Get current authenticated user from auth state
    final user = _getCurrentAuthenticatedUser();

    if (user == null) {
      debugPrint('[CartRepository] No authenticated user found');
      return const Left(AuthFailure(message: 'Authentication required'));
    }

    if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
      debugPrint('[CartRepository] User does not have backend authentication');
      return const Left(AuthFailure(message: 'Backend authentication required'));
    }

    final backendToken = user.backendToken!;
    debugPrint('[CartRepository] ✅ BULK MULTIPLE SELECTION: Using fetch-current-cart-then-send-full-cart pattern');
    debugPrint('[CartRepository] Updating selection for ${variantIds.length} items to selected: $selected');

    try {
      // Step 1: Get current cart state
      debugPrint('[CartRepository] ✅ BULK MULTIPLE SELECTION: Step 1 - Fetching current cart state');
      final currentCartResult = await getCart();

      return await currentCartResult.fold(
        (failure) {
          debugPrint('[CartRepository] ✅ BULK MULTIPLE SELECTION: Failed to get current cart: ${failure.message}');
          return Left(failure);
        },
        (currentCart) async {
          debugPrint('[CartRepository] ✅ BULK MULTIPLE SELECTION: Step 2 - Applying selection updates to local cart state');

          // Step 2: Apply selection updates to all items in cart
          final updatedItems = currentCart.items.map((item) {
            final shouldUpdateSelection = variantIds.contains(item.variantId);
            final newSelected = shouldUpdateSelection ? selected : item.selected;

            debugPrint('[CartRepository] Item ${item.variantId}: selected = $newSelected ${shouldUpdateSelection ? '(updated)' : '(unchanged)'}');

            return CartItemUpdate(
              variantId: item.variantId,
              productId: item.productId,
              quantity: item.quantity,
              price: item.price,
              title: item.title,
              imageUrl: item.imageUrl,
              selected: newSelected,
            );
          }).toList();

          debugPrint('[CartRepository] ✅ BULK MULTIPLE SELECTION: Step 3 - Sending all ${updatedItems.length} items via bulk operation');

          // Step 3: Send all items with updated selections via bulk operation
          return await _modernCartService.updateMultipleItems(
            items: updatedItems,
            backendToken: backendToken,
          );
        },
      );
    } catch (e) {
      debugPrint('[CartRepository] ✅ BULK MULTIPLE SELECTION: Error in bulk operation: $e');
      return Left(UnknownFailure(message: 'Failed to update multiple items selection: $e'));
    }
  }

  @override
  Future<Either<Failure, Cart>> selectAllItems() async {
    // Get current authenticated user from auth state
    final user = _getCurrentAuthenticatedUser();

    if (user == null) {
      debugPrint('[CartRepository] No authenticated user found');
      return const Left(AuthFailure(message: 'Authentication required'));
    }

    if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
      debugPrint('[CartRepository] User does not have backend authentication');
      return const Left(AuthFailure(message: 'Backend authentication required'));
    }

    final backendToken = user.backendToken!;
    debugPrint('[CartRepository] ✅ BULK SELECT ALL: Using fetch-current-cart-then-send-full-cart pattern');

    try {
      // Step 1: Get current cart state
      debugPrint('[CartRepository] ✅ BULK SELECT ALL: Step 1 - Fetching current cart state');
      final cartResult = await getCart();

      return await cartResult.fold(
        (failure) {
          debugPrint('[CartRepository] ✅ BULK SELECT ALL: Failed to get current cart: ${failure.message}');
          return Left(failure);
        },
        (cart) async {
          debugPrint('[CartRepository] ✅ BULK SELECT ALL: Step 2 - Applying select all to local cart state');

          // Step 2: Create CartItemUpdate objects with all items selected
          final items = cart.items.map((item) => CartItemUpdate(
            variantId: item.variantId,
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            title: item.title,
            imageUrl: item.imageUrl,
            selected: true, // Select all items
          )).toList();

          debugPrint('[CartRepository] ✅ BULK SELECT ALL: Step 3 - Sending all ${items.length} items via bulk operation');

          // Step 3: Send all items with selected=true via bulk operation
          return await _modernCartService.updateMultipleItems(
            items: items,
            backendToken: backendToken,
          );
        },
      );
    } catch (e) {
      debugPrint('[CartRepository] ✅ BULK SELECT ALL: Error in bulk operation: $e');
      return Left(UnknownFailure(message: 'Failed to select all items: $e'));
    }
  }

  @override
  Future<Either<Failure, Cart>> unselectAllItems() async {
    // Get current authenticated user from auth state
    final user = _getCurrentAuthenticatedUser();

    if (user == null) {
      debugPrint('[CartRepository] No authenticated user found');
      return const Left(AuthFailure(message: 'Authentication required'));
    }

    if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
      debugPrint('[CartRepository] User does not have backend authentication');
      return const Left(AuthFailure(message: 'Backend authentication required'));
    }

    final backendToken = user.backendToken!;
    debugPrint('[CartRepository] ✅ BULK UNSELECT ALL: Using fetch-current-cart-then-send-full-cart pattern');

    try {
      // Step 1: Get current cart state
      debugPrint('[CartRepository] ✅ BULK UNSELECT ALL: Step 1 - Fetching current cart state');
      final cartResult = await getCart();

      return await cartResult.fold(
        (failure) {
          debugPrint('[CartRepository] ✅ BULK UNSELECT ALL: Failed to get current cart: ${failure.message}');
          return Left(failure);
        },
        (cart) async {
          debugPrint('[CartRepository] ✅ BULK UNSELECT ALL: Step 2 - Applying unselect all to local cart state');

          // Step 2: Create CartItemUpdate objects with all items unselected
          final items = cart.items.map((item) => CartItemUpdate(
            variantId: item.variantId,
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            title: item.title,
            imageUrl: item.imageUrl,
            selected: false, // Unselect all items
          )).toList();

          debugPrint('[CartRepository] ✅ BULK UNSELECT ALL: Step 3 - Sending all ${items.length} items via bulk operation');

          // Step 3: Send all items with selected=false via bulk operation
          return await _modernCartService.updateMultipleItems(
            items: items,
            backendToken: backendToken,
          );
        },
      );
    } catch (e) {
      debugPrint('[CartRepository] ✅ BULK UNSELECT ALL: Error in bulk operation: $e');
      return Left(UnknownFailure(message: 'Failed to unselect all items: $e'));
    }
  }
}
