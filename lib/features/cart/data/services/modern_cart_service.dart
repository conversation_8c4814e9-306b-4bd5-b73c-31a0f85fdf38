import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/cart.dart';

/// Modern cart service using unified PUT /api/cart endpoint
/// Replaces all legacy cart endpoints (add_item, update_item, remove_item)
class ModernCartService {
  final Dio _dio;

  ModernCartService({required Dio dio}) : _dio = dio;

  /// Add item to cart using modern PUT /api/cart endpoint
  Future<Either<Failure, Cart>> addItem({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    String? imageUrl,
    required String backendToken,
  }) async {
    try {
      debugPrint('[ModernCartService] Adding item: $variantId (qty: $quantity, price: $price)');

      final itemData = {
        'variant_id': variantId,
        'product_id': productId,
        'quantity': quantity,
        'price': price,
        'title': title,
        'selected': true,
      };

      if (imageUrl != null) {
        itemData['image_url'] = imageUrl;
      }

      final response = await _dio.put(
        '/cart',
        data: {
          'items': [itemData],
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[ModernCartService] Add item request data: $itemData');
      return _parseCartResponse(response);
    } on DioException catch (e) {
      return Left(_handleDioError(e, 'add item'));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to add item: $e'));
    }
  }



  /// Update item quantity with complete data using modern PUT /api/cart endpoint
  /// This is the CORRECT method to use with complete item data
  Future<Either<Failure, Cart>> updateItemWithCompleteData({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    String? imageUrl,
    bool? selected,
    required String backendToken,
  }) async {
    try {
      debugPrint('[ModernCartService] Updating item with complete data: $variantId (qty: $quantity, price: $price)');

      final itemData = {
        'variant_id': variantId,
        'product_id': productId,
        'quantity': quantity,
        'price': price,
        'title': title,
        'selected': selected ?? true,
      };

      if (imageUrl != null) {
        itemData['image_url'] = imageUrl;
      }

      final response = await _dio.put(
        '/cart',
        data: {
          'items': [itemData],
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[ModernCartService] Update item request data: $itemData');
      return _parseCartResponse(response);
    } on DioException catch (e) {
      return Left(_handleDioError(e, 'update item with complete data'));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to update item with complete data: $e'));
    }
  }

  /// Remove item from cart using modern PUT /api/cart endpoint
  Future<Either<Failure, Cart>> removeItem({
    required String variantId,
    required String backendToken,
  }) async {
    try {
      debugPrint('[ModernCartService] Removing item: $variantId');
      
      final response = await _dio.put(
        '/cart',
        data: {
          'items': [
            {
              'variant_id': variantId,
              'quantity': 0, // Setting quantity to 0 removes the item
            }
          ],
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      return _parseCartResponse(response);
    } on DioException catch (e) {
      return Left(_handleDioError(e, 'remove item'));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to remove item: $e'));
    }
  }

  /// Update multiple items in a single request (bulk operation)
  ///
  /// Note: Voucher functionality has been removed from the backend.
  Future<Either<Failure, Cart>> updateMultipleItems({
    required List<CartItemUpdate> items,
    required String backendToken,
    String? paymentMethod,
  }) async {
    try {
      debugPrint('[ModernCartService] ✅ BULK UPDATE: Updating ${items.length} items in single request');
      debugPrint('[ModernCartService] ✅ BULK UPDATE: This should send ALL cart items to backend');

      final itemsData = items.map((item) => item.toJson()..['selected'] = item.selected ?? true).toList();
      debugPrint('[ModernCartService] ✅ BULK UPDATE: Items data: $itemsData');

      final requestData = <String, dynamic>{
        'items': itemsData,
      };

      // Add optional parameters (voucher functionality removed)
      if (paymentMethod != null) {
        requestData['preferred_payment_method'] = paymentMethod;
      }

      final response = await _dio.put(
        '/cart',
        data: requestData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      return _parseCartResponse(response);
    } on DioException catch (e) {
      return Left(_handleDioError(e, 'bulk update items'));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to bulk update items: $e'));
    }
  }



  /// Update item selection status using modern PUT /api/cart endpoint
  Future<Either<Failure, Cart>> updateItemSelection({
    required String variantId,
    required bool selected,
    required String backendToken,
  }) async {
    try {
      debugPrint('[ModernCartService] WARNING: Using updateItemSelection without quantity!');
      debugPrint('[ModernCartService] This may cause item $variantId to disappear from cart!');
      debugPrint('[ModernCartService] Updating item selection: $variantId -> $selected');

      final response = await _dio.put(
        '/cart',
        data: {
          'items': [
            {
              'variant_id': variantId,
              'selected': selected,
            }
          ],
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      return _parseCartResponse(response);
    } on DioException catch (e) {
      return Left(_handleDioError(e, 'update item selection'));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to update item selection: $e'));
    }
  }

  /// Update item selection status with complete data using modern PUT /api/cart endpoint
  /// This is the CORRECT method to use to avoid items disappearing
  Future<Either<Failure, Cart>> updateItemSelectionWithQuantity({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    String? imageUrl,
    required bool selected,
    required String backendToken,
  }) async {
    try {
      debugPrint('[ModernCartService] Updating item selection with complete data: $variantId -> selected: $selected, quantity: $quantity, price: $price');

      final itemData = {
        'variant_id': variantId,
        'product_id': productId,
        'quantity': quantity,
        'price': price,
        'title': title,
        'selected': selected,
      };

      if (imageUrl != null) {
        itemData['image_url'] = imageUrl;
      }

      final response = await _dio.put(
        '/cart',
        data: {
          'items': [itemData],
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[ModernCartService] Selection update request data: $itemData');
      debugPrint('[ModernCartService] Response status: ${response.statusCode}');
      return _parseCartResponse(response);
    } on DioException catch (e) {
      return Left(_handleDioError(e, 'update item selection with complete data'));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to update item selection with complete data: $e'));
    }
  }



  /// Update multiple items selection status with complete data using modern PUT /api/cart endpoint
  /// This is the CORRECT method to use with complete item data
  Future<Either<Failure, Cart>> updateMultipleItemsSelectionWithCompleteData({
    required List<CartItemUpdate> items,
    required bool selected,
    required String backendToken,
  }) async {
    try {
      debugPrint('[ModernCartService] Updating selection for ${items.length} items with complete data');

      final response = await _dio.put(
        '/cart',
        data: {
          'items': items.map((item) => item.toJson()..['selected'] = selected).toList(),
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[ModernCartService] Multiple selection update request data: ${items.map((item) => item.toJson()..['selected'] = selected).toList()}');
      return _parseCartResponse(response);
    } on DioException catch (e) {
      return Left(_handleDioError(e, 'update multiple items selection with complete data'));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to update multiple items selection with complete data: $e'));
    }
  }

  /// Select all items in cart
  Future<Either<Failure, Cart>> selectAllItems({
    required List<CartItemUpdate> items,
    required String backendToken,
  }) async {
    try {
      debugPrint('[ModernCartService] Selecting all ${items.length} items');

      final response = await _dio.put(
        '/cart',
        data: {
          'items': items.map((item) => item.toJson()..['selected'] = true).toList(),
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[ModernCartService] Select all request data: ${items.map((item) => item.toJson()..['selected'] = true).toList()}');
      return _parseCartResponse(response);
    } on DioException catch (e) {
      return Left(_handleDioError(e, 'select all items'));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to select all items: $e'));
    }
  }

  /// Unselect all items in cart
  Future<Either<Failure, Cart>> unselectAllItems({
    required List<CartItemUpdate> items,
    required String backendToken,
  }) async {
    try {
      debugPrint('[ModernCartService] Unselecting all ${items.length} items');

      final response = await _dio.put(
        '/cart',
        data: {
          'items': items.map((item) => item.toJson()..['selected'] = false).toList(),
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[ModernCartService] Unselect all request data: ${items.map((item) => item.toJson()..['selected'] = false).toList()}');
      return _parseCartResponse(response);
    } on DioException catch (e) {
      return Left(_handleDioError(e, 'unselect all items'));
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to unselect all items: $e'));
    }
  }

  /// Parse cart response from API
  Either<Failure, Cart> _parseCartResponse(Response response) {
    try {
      final responseData = response.data;

      if (responseData == null) {
        return const Left(ServerFailure(message: 'Empty response from server'));
      }

      // Handle both old and new response formats
      Map<String, dynamic> cartData;
      if (responseData is Map<String, dynamic>) {
        if (responseData.containsKey('success') && responseData['success'] == true) {
          // New format: {"success": true, "data": {...}}
          cartData = responseData['data'] as Map<String, dynamic>;
        } else if (responseData.containsKey('data')) {
          // Historic format: {"data": {...}}
          cartData = responseData['data'] as Map<String, dynamic>;
        } else {
          // Direct cart data
          cartData = responseData;
        }
      } else {
        return const Left(ServerFailure(message: 'Invalid response format'));
      }

      final cart = Cart.fromJson(cartData);
      debugPrint('[ModernCartService] Successfully parsed cart with ${cart.items.length} items');
      return Right(cart);
    } catch (e) {
      debugPrint('[ModernCartService] Failed to parse cart response: $e');
      return Left(ServerFailure(message: 'Failed to parse cart data: $e'));
    }
  }

  /// Handle Dio errors with enhanced error messages
  Failure _handleDioError(DioException error, String operation) {
    debugPrint('[ModernCartService] DioException during $operation: ${error.type}');
    debugPrint('[ModernCartService] Status code: ${error.response?.statusCode}');
    debugPrint('[ModernCartService] Error data: ${error.response?.data}');

    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    // Handle authentication errors
    if (statusCode == 401) {
      return const AuthFailure(message: 'Authentication required');
    }

    // Handle validation errors (422)
    if (statusCode == 422) {
      String errorMessage = 'Validation failed';

      if (responseData is Map<String, dynamic>) {
        final apiError = responseData['error'] as String?;
        final errorCode = responseData['error_code'] as String?;
        final details = responseData['details'] as Map<String, dynamic>?;

        if (apiError != null) {
          errorMessage = apiError;
        } else if (errorCode != null) {
          errorMessage = _getValidationErrorMessage(errorCode, details) ?? errorMessage;
        }
      }

      return ServerFailure(message: 'Failed to $operation: $errorMessage');
    }

    // Handle other HTTP errors
    if (statusCode != null) {
      String errorMessage = 'Server error';

      if (responseData is Map<String, dynamic>) {
        final apiError = responseData['error'] as String?;
        if (apiError != null) {
          errorMessage = apiError;
        }
      }

      return ServerFailure(message: 'Failed to $operation: $errorMessage (HTTP $statusCode)');
    }

    // Handle network errors
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ServerFailure(message: 'Network timeout while trying to $operation');
      case DioExceptionType.connectionError:
        return ServerFailure(message: 'Connection failed while trying to $operation');
      default:
        return ServerFailure(message: 'Failed to $operation: ${error.message}');
    }
  }

  /// Get user-friendly validation error messages
  ///
  /// Note: Voucher functionality has been removed from the backend.
  String? _getValidationErrorMessage(String errorCode, Map<String, dynamic>? details) {
    switch (errorCode) {
      case 'ITEM_NOT_FOUND':
        return 'Item not found in cart';
      case 'INVALID_QUANTITY':
        return 'Invalid quantity. Must be a positive number';
      case 'INVALID_PAYMENT_METHOD':
        return 'Invalid payment method selected';
      case 'CART_NOT_FOUND':
        return 'Cart not found';
      case 'INSUFFICIENT_STOCK':
        final variantId = details?['variant_id'] as String?;
        final availableStock = details?['available_stock'] as int?;
        if (variantId != null && availableStock != null) {
          return 'Only $availableStock items available for this product';
        }
        return 'Insufficient stock for requested quantity';
      // Voucher functionality has been removed from the backend
      case 'INVALID_VOUCHER':
      case 'VOUCHER_NOT_APPLICABLE':
        debugPrint('[ModernCartService] ❌ Voucher error detected - voucher functionality has been removed');
        return 'Voucher functionality has been removed from the backend';
      default:
        return null;
    }
  }
}

/// Cart item update model for bulk operations
class CartItemUpdate {
  final String variantId;
  final String? productId;
  final int quantity;
  final double? price;
  final String? title;
  final String? imageUrl;
  final bool? selected;

  const CartItemUpdate({
    required this.variantId,
    this.productId,
    required this.quantity,
    this.price,
    this.title,
    this.imageUrl,
    this.selected,
  });

  Map<String, dynamic> toJson() {
    return {
      'variant_id': variantId,
      if (productId != null) 'product_id': productId,
      'quantity': quantity,
      if (price != null) 'price': price,
      if (title != null) 'title': title,
      if (imageUrl != null) 'image_url': imageUrl,
      if (selected != null) 'selected': selected,
    };
  }
}

/// Cart item selection update model for selection-only operations
class CartItemSelectionUpdate {
  final String variantId;
  final bool selected;

  const CartItemSelectionUpdate({
    required this.variantId,
    required this.selected,
  });

  Map<String, dynamic> toJson() {
    return {
      'variant_id': variantId,
      'selected': selected,
    };
  }
}
