import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../l10n/app_localizations.dart';

/// Simplified widget that only handles button rendering and basic state
/// Delegates all business logic to external handlers
class PurchaseButtonsWidget extends StatelessWidget {
  final VoidCallback onAddToCart;
  final VoidCallback onBuyNow;
  final bool isAddToCartLoading;
  final bool isBuyNowLoading;

  const PurchaseButtonsWidget({
    super.key,
    required this.onAddToCart,
    required this.onBuyNow,
    this.isAddToCartLoading = false,
    this.isBuyNowLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final isAnyLoading = isAddToCartLoading || isBuyNowLoading;

    return Row(
      children: [
        // Add to Cart button
        Expanded(
          child: OutlinedButton(
            onPressed: isAnyLoading ? null : onAddToCart,
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: isAnyLoading
                    ? Colors.grey.shade400
                    : const Color(0xFFFF6982),
                width: 1
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(vertical: 14),
            ),
            child: isAddToCartLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6982)),
                    ),
                  )
                : Text(
                    AppLocalizations.of(context).addToCart,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isAnyLoading
                          ? Colors.grey.shade400
                          : const Color(0xFFFF6982),
                      height: 1.43,
                    ),
                  ),
          ),
        ),

        const SizedBox(width: 12),

        // Buy Now button
        Expanded(
          child: ElevatedButton(
            onPressed: isAnyLoading ? null : onBuyNow,
            style: ElevatedButton.styleFrom(
              backgroundColor: isAnyLoading
                  ? Colors.grey.shade400
                  : const Color(0xFFFF6982),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(vertical: 14),
              elevation: 0,
            ),
            child: isBuyNowLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    AppLocalizations.of(context).buyNow,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                      height: 1.43,
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}
