import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../domain/models/purchase_config.dart';
import '../providers/purchase_provider.dart';
import 'quantity_selector.dart';
import '../../../../core/utils/price_formatter.dart';
import '../../../../l10n/app_localizations.dart';

/// Quantity and price section widget for the purchase modal
/// Handles the gray section with quantity controls and total payment display
class QuantityPriceSection extends StatelessWidget {
  final PurchaseConfig purchaseState;
  final PurchaseNotifier purchaseNotifier;
  final String productCurrency;

  const QuantityPriceSection({
    super.key,
    required this.purchaseState,
    required this.purchaseNotifier,
    required this.productCurrency,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF9FAFB),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Quantity selector with fixed width
          SizedBox(
            width: 120,
            child: QuantitySelector(
              quantity: purchaseState.quantity,
              onIncrement: () {
                // debugPrint('🔢 Incrementing quantity from ${purchaseState.quantity} to ${purchaseState.quantity + 1}');
                purchaseNotifier.incrementQuantity();
              },
              onDecrement: () {
                debugPrint('🔢 Decrementing quantity from ${purchaseState.quantity} to ${purchaseState.quantity - 1}');
                purchaseNotifier.decrementQuantity();
              },
            ),
          ),
          // Flexible total payment section
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  AppLocalizations.of(context).totalPayment,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    alignment: Alignment.centerRight,
                    child: Text(
                      _formatPrice(purchaseState.totalPrice, productCurrency),
                      style: GoogleFonts.inter(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF111827),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatPrice(double amount, String currency) {
    return PriceFormatter.formatPrice(amount.toStringAsFixed(2), currency);
  }
}
