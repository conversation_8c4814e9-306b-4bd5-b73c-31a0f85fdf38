import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/models/product_model.dart';
import '../../domain/models/purchase_config.dart';
import '../providers/purchase_provider.dart';
import 'selection_dropdown.dart';
import 'esim_type_selector.dart';
import '../../../../l10n/app_localizations.dart';

/// Form section widget for the purchase modal
/// Handles product configuration options like data, days, and eSIM type selection
class PurchaseFormSection extends ConsumerWidget {
  final Product product;
  final PurchaseConfig purchaseState;
  final PurchaseNotifier purchaseNotifier;
  final StateNotifierProvider<PurchaseNotifier, PurchaseConfig> purchaseStateProvider;
  final VoidCallback onShowDetails;

  const PurchaseFormSection({
    super.key,
    required this.product,
    required this.purchaseState,
    required this.purchaseNotifier,
    required this.purchaseStateProvider,
    required this.onShowDetails,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final iccidOptions = ref.watch(iccidOptionsProvider); // Commented out as it's not currently used

    // Get variants for dynamic dropdown options
    final variants = product.variants.edges.map((edge) => edge.node).toList();
    final availableVariants = variants.where((variant) => variant.availableForSale).toList();

    final dataOptions = ref.watch(smartDataOptionsProvider(availableVariants));
    final daysOptions = ref.watch(smartDaysOptionsProvider((
      variants: availableVariants,
      selectedData: purchaseState.selectedData
    )));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // See details button (positioned after product title)
        Align(
          alignment: Alignment.center,
          child: TextButton(
            onPressed: onShowDetails,
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              AppLocalizations.of(context).seeDetails,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFFFF6982),
                decoration: TextDecoration.underline,
                decorationColor: const Color(0xFFFF6982),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Selection dropdowns row
        Row(
          children: [
            Expanded(
              child: SelectionDropdown(
                label: AppLocalizations.of(context).yourDataLabel,
                value: purchaseState.selectedData,
                options: dataOptions,
                onChanged: (value) {
                  if (value != null) {
                    purchaseNotifier.updateData(value);
                  }
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: SelectionDropdown(
                label: AppLocalizations.of(context).dayLabel,
                value: purchaseState.selectedDays,
                options: daysOptions,
                onChanged: (value) {
                  if (value != null) {
                    purchaseNotifier.updateDays(value);
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // eSIM type selector
        ESimTypeSelector(
          selectedType: purchaseState.eSimType,
          onChanged: (type) {
            debugPrint('🔄 eSIM type changed to: ${type.displayName}');
            purchaseNotifier.updateESimType(type);
          },
        ),
        const SizedBox(height: 16),

        // Conditional ICCID dropdown - COMMENTED OUT
        // if (purchaseState.eSimType == ESimType.topUp) ...[
        //   const SizedBox(height: 16),
        //   SelectionDropdown(
        //     label: 'ICCID for Top Up',
        //     value: purchaseState.selectedIccid ?? 'Select your TravelGator eSIM',
        //     options: iccidOptions,
        //     onChanged: (value) {
        //       debugPrint('🔄 ICCID changed to: $value');
        //       purchaseNotifier.updateIccid(value);
        //     },
        //   ),
        //   // const SizedBox(height: 8), // Add spacing before gray section
        // ],
      ],
    );
  }
}
