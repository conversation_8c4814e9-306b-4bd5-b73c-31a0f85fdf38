import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/models/product_model.dart';
import '../../domain/models/purchase_config.dart';
import '../providers/purchase_provider.dart';
import '../services/cart_service.dart';
import 'purchase_actions.dart';

/// Button section widget for the purchase modal
/// Handles the purchase actions (Add to Cart and Buy Now) with unified logic
class PurchaseButtonSection extends ConsumerWidget {
  final Product product;
  final PurchaseConfig purchaseState;
  final StateNotifierProvider<PurchaseNotifier, PurchaseConfig> purchaseStateProvider;
  final DateTime? lastAddToCartClick;
  final Function(DateTime?) onLastAddToCartClickChanged;

  const PurchaseButtonSection({
    super.key,
    required this.product,
    required this.purchaseState,
    required this.purchaseStateProvider,
    required this.lastAddToCartClick,
    required this.onLastAddToCartClickChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      child: PurchaseActions(
        onAddToCart: () => _handleAddToCart(context, ref),
        variantId: _getVariantId(),
        productId: product.id,
        quantity: purchaseState.quantity,
        price: purchaseState.unitPrice,
        title: _buildProductTitle(),
        imageUrl: product.featuredImage?.url,
        onCloseModal: () => _handleModalClose(context),
      ),
    );
  }

  /// Handles add to cart action with proper error handling and state management
  Future<void> _handleAddToCart(BuildContext context, WidgetRef ref) async {
    await CartService.handleAddToCart(
      context: context,
      ref: ref,
      product: product,
      purchaseState: purchaseState,
      setHandlingOperation: (bool handling) {
        // No-op: CartService handles its own loading feedback
      },
      lastAddToCartClick: lastAddToCartClick,
      setLastAddToCartClick: onLastAddToCartClickChanged,
    );
  }

  /// Gets the variant ID for the current purchase state
  String? _getVariantId() {
    return purchaseState.selectedVariantId ?? 
           (product.variants.edges.isNotEmpty ? product.variants.edges.first.node.id : null);
  }

  /// Builds the complete product title with all selected options
  String _buildProductTitle() {
    return '${product.title} - ${purchaseState.selectedVariantTitle} - ${purchaseState.selectedData} for ${purchaseState.selectedDays} (${purchaseState.eSimType.displayName})';
  }

  /// Handles modal close with proper navigation safety
  void _handleModalClose(BuildContext context) {
    // Use post-frame callback to avoid Navigator lock issues
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        try {
          Navigator.of(context).pop();
        } catch (e) {
          debugPrint('[PurchaseButtonSection] Could not close modal: $e');
        }
      }
    });
  }
}
