import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/models/product_model.dart';
import '../../domain/models/purchase_config.dart';
import '../providers/purchase_provider.dart';
import 'purchase_header.dart';
import 'product_details_view.dart';
import 'purchase_form_section.dart';
import 'quantity_price_section.dart';
import 'purchase_button_section.dart';


/// Main content widget for the purchase modal
/// Handles the overall layout structure and content switching between form and details view
class PurchaseModalContent extends ConsumerWidget {
  final Product product;
  final bool showDetails;
  final VoidCallback onToggleDetails;
  final StateNotifierProvider<PurchaseNotifier, PurchaseConfig> purchaseStateProvider;
  final DateTime? lastAddToCartClick;
  final Function(DateTime?) onLastAddToCartClickChanged;

  const PurchaseModalContent({
    super.key,
    required this.product,
    required this.showDetails,
    required this.onToggleDetails,
    required this.purchaseStateProvider,
    required this.lastAddToCartClick,
    required this.onLastAddToCartClickChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final purchaseState = ref.watch(purchaseStateProvider);
    final purchaseNotifier = ref.read(purchaseStateProvider.notifier);
    final productCurrency = _getProductCurrency(product);

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.9,
            minHeight: MediaQuery.of(context).size.height * 0.3,
          ),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: IntrinsicHeight(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                PurchaseHeader(showDetails: showDetails),
                // Bottom section (matching Figma structure)
                Flexible(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Content section (white background)
                      Flexible(
                        child: SingleChildScrollView(
                          child: Container(
                            color: Colors.white,
                            padding: const EdgeInsets.fromLTRB(20, 20, 20, 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Product title (matching Figma exactly)
                                Text(
                                  purchaseState.productTitle,
                                  style: GoogleFonts.inter(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF1F2937),
                                    height: 1.21,
                                  ),
                                ),
                                const SizedBox(height: 16),

                                // Show details view or form based on state
                                if (showDetails) ...[
                                  ProductDetailsView(
                                    product: product,
                                    onHideDetails: onToggleDetails,
                                  ),
                                ] else ...[
                                  PurchaseFormSection(
                                    product: product,
                                    purchaseState: purchaseState,
                                    purchaseNotifier: purchaseNotifier,
                                    purchaseStateProvider: purchaseStateProvider,
                                    onShowDetails: onToggleDetails,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ),
                      // Show quantity/price and buttons only when not in details view
                      if (!showDetails) ...[
                        QuantityPriceSection(
                          purchaseState: purchaseState,
                          purchaseNotifier: purchaseNotifier,
                          productCurrency: productCurrency,
                        ),
                        PurchaseButtonSection(
                          product: product,
                          purchaseState: purchaseState,
                          purchaseStateProvider: purchaseStateProvider,
                          lastAddToCartClick: lastAddToCartClick,
                          onLastAddToCartClickChanged: onLastAddToCartClickChanged,
                        ),
                      ] else ...[
                        // Button section for details view
                        PurchaseButtonSection(
                          product: product,
                          purchaseState: purchaseState,
                          purchaseStateProvider: purchaseStateProvider,
                          lastAddToCartClick: lastAddToCartClick,
                          onLastAddToCartClickChanged: onLastAddToCartClickChanged,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getProductCurrency(Product product) {
    try {
      if (product.variants.edges.isNotEmpty) {
        final variant = product.variants.edges.first.node;
        return variant.price.currencyCode;
      }
    } catch (e) {
      debugPrint('Error getting product currency: $e');
    }
    return 'USD'; // Default fallback
  }
}
