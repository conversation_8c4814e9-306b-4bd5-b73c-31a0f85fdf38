import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../payment/presentation/screens/shopify_checkout_webview.dart';

/// Service to handle navigation for purchase checkout flows
/// Centralizes Shopify checkout navigation, WebView routing, and modal management
class NavigationService {
  final BuildContext context;
  final VoidCallback? onCloseModal;

  NavigationService({
    required this.context,
    this.onCloseModal,
  });

  /// Navigate to Shopify checkout WebView via route (recommended approach)
  /// Returns true if checkout was successful, false if cancelled/failed
  Future<bool> navigateToShopifyCheckoutViaRoute(String checkoutUrl, String orderId) async {
    try {
      debugPrint('[NavigationService] Using route-based navigation for WebView');

      // Check context is still mounted
      if (!context.mounted) {
        debugPrint('[NavigationService] ❌ Context not mounted for route navigation');
        return false;
      }

      // Use route-based navigation (same as cart checkout uses)
      final result = await context.push('/checkout-webview', extra: {
        'checkoutUrl': checkoutUrl,
        'orderId': orderId,
        'totalAmount': 0.0, // Not needed for Buy Now
        'currency': 'USD',
        'isFromBuyNow': true,
        'onCloseModal': onCloseModal, // Pass modal close callback
      });

      debugPrint('[NavigationService] Route-based WebView result: $result');
      return result == 'success';

    } catch (e) {
      debugPrint('[NavigationService] ❌ Error in route-based navigation: $e');
      return false;
    }
  }

  /// Navigate to Shopify checkout WebView with direct MaterialPageRoute (legacy approach)
  /// Returns true if checkout was successful, false if cancelled/failed
  Future<bool> navigateToShopifyCheckoutDirect(String checkoutUrl, String orderId) async {
    try {
      debugPrint('[NavigationService] Opening Shopify checkout WebView directly');

      // Check if context is still mounted before navigation
      if (!context.mounted) {
        debugPrint('[NavigationService] ❌ Context not mounted for Shopify navigation');
        return false;
      }

      debugPrint('[NavigationService] ✅ Context is mounted, proceeding with navigation');

      final result = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => ShopifyCheckoutWebView(
            checkoutUrl: checkoutUrl,
            orderId: orderId,
            onSuccess: (completedOrderId) {
              debugPrint('[NavigationService] ✅ Shopify checkout successful: $completedOrderId');
              if (context.mounted) {
                Navigator.of(context).pop(true);
              }
            },
            onError: (error) {
              debugPrint('[NavigationService] ❌ Shopify checkout error: $error');
              if (context.mounted) {
                Navigator.of(context).pop(false);
              }
            },
            onCancel: () {
              debugPrint('[NavigationService] ⚠️ Shopify checkout cancelled');
              if (context.mounted) {
                Navigator.of(context).pop(false);
              }
            },
            onLoadingEnd: () {
              // Close modal once WebView starts loading (safe timing)
              if (onCloseModal != null) {
                debugPrint('[NavigationService] 🔄 Closing review modal after WebView starts loading');
                onCloseModal!();
              }
            },
          ),
        ),
      );

      return result ?? false;
    } catch (e) {
      debugPrint('[NavigationService] ❌ Error opening Shopify checkout: $e');
      debugPrint('[NavigationService] ❌ Error details: ${e.toString()}');

      // Check if the error is related to widget disposal
      if (e.toString().contains('unmounted') || e.toString().contains('defunct')) {
        debugPrint('[NavigationService] ❌ Widget was disposed during navigation attempt');
        debugPrint('[NavigationService] ❌ This usually means the user navigated away during the API call');
      }

      return false;
    }
  }

  /// Navigate to WebView checkout with comprehensive parameters
  Future<String?> navigateToWebViewCheckout({
    required String checkoutUrl,
    required String orderId,
    required double totalAmount,
    required String currency,
    bool isFromBuyNow = false,
  }) async {
    try {
      debugPrint('[NavigationService] Starting WebView checkout flow');
      debugPrint('[NavigationService] Checkout URL: $checkoutUrl');

      // Validate checkout URL
      if (checkoutUrl.isEmpty) {
        throw Exception('Empty checkout URL received');
      }

      // Navigate to WebView checkout
      if (context.mounted) {
        debugPrint('[NavigationService] 🔍 Navigation parameters:');
        debugPrint('[NavigationService]   checkoutUrl: $checkoutUrl');
        debugPrint('[NavigationService]   orderId: $orderId');
        debugPrint('[NavigationService]   totalAmount: $totalAmount');
        debugPrint('[NavigationService]   currency: $currency');

        final result = await context.push('/checkout-webview', extra: {
          'checkoutUrl': checkoutUrl,
          'orderId': orderId,
          'totalAmount': totalAmount,
          'currency': currency,
          'isFromBuyNow': isFromBuyNow,
        });

        debugPrint('[NavigationService] Checkout WebView result: $result');
        return result?.toString();
      }
      return null;
    } catch (e) {
      debugPrint('[NavigationService] Error in WebView checkout flow: $e');
      rethrow;
    }
  }

  /// Navigate to home page
  void navigateToHome() {
    if (context.mounted) {
      debugPrint('[NavigationService] 🏠 Navigating to home page');
      context.go('/home');
    }
  }

  /// Navigate to home page with root navigator
  void navigateToHomeWithRootNavigator() {
    try {
      final rootContext = Navigator.of(context, rootNavigator: true).context;
      if (rootContext.mounted) {
        debugPrint('[NavigationService] 🏠 Navigating to home page with root navigator');
        GoRouter.of(rootContext).go('/');
      }
    } catch (e) {
      debugPrint('[NavigationService] ❌ Error navigating to home with root navigator: $e');
    }
  }

  /// Close modal safely
  void closeModal() {
    if (onCloseModal != null) {
      debugPrint('[NavigationService] 🔄 Closing modal');
      onCloseModal!();
    }
  }

  /// Pop current route safely
  void popRoute([dynamic result]) {
    if (context.mounted) {
      debugPrint('[NavigationService] ⬅️ Popping current route');
      Navigator.of(context).pop(result);
    }
  }

  /// Check if context is mounted and safe for navigation
  bool get isContextMounted => context.mounted;

  /// Get root navigator context
  BuildContext get rootNavigatorContext => Navigator.of(context, rootNavigator: true).context;
}
