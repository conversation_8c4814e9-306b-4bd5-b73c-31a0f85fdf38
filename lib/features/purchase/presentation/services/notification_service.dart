import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../l10n/app_localizations.dart';

/// Service to handle all purchase-related notifications
/// Centralizes success notifications, error messages, and processing indicators
class NotificationService {
  final BuildContext context;

  NotificationService(this.context);

  /// Show processing notification for checkout
  void showProcessingNotification() {
    debugPrint('[NotificationService] 🔄 SHOWING processing checkout notification');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 12),
            Text(AppLocalizations.of(context).processingCheckout),
          ],
        ),
        backgroundColor: const Color(0xFFFF6982),
        duration: const Duration(minutes: 5), // Longer duration to prevent auto-dismissal
      ),
    );
  }

  /// Hide processing notification
  void hideProcessingNotification() {
    debugPrint('[NotificationService] ❌ HIDING processing checkout notification');
    if (context.mounted) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
    }
  }

  /// Show Buy Now success notification after WebView completion
  void showBuyNowSuccessNotificationAfterWebView(String orderId) {
    try {
      debugPrint('[NotificationService] 📢 ENTERING: showBuyNowSuccessNotificationAfterWebView');
      debugPrint('[NotificationService] 📢 Order ID: $orderId');
      debugPrint('[NotificationService] 📢 Context mounted: ${context.mounted}');

      if (!context.mounted) {
        debugPrint('[NotificationService] ❌ Context not mounted - cannot show notification');
        return;
      }

      debugPrint('[NotificationService] 📢 About to clear SnackBars...');
      ScaffoldMessenger.of(context).clearSnackBars();
      debugPrint('[NotificationService] ✅ SnackBars cleared');

      debugPrint('[NotificationService] 📢 About to show SnackBar...');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).paymentSuccessful),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
        ),
      );
      debugPrint('[NotificationService] ✅ SnackBar shown successfully');

      // Navigate to home page
      if (context.mounted) {
        debugPrint('[NotificationService] 🏠 About to navigate to home page...');
        context.go('/home');
        debugPrint('[NotificationService] ✅ Navigation to home completed');
      } else {
        debugPrint('[NotificationService] ❌ Context unmounted before home navigation');
      }

      debugPrint('[NotificationService] 📢 EXITING: showBuyNowSuccessNotificationAfterWebView');

    } catch (e, stackTrace) {
      debugPrint('[NotificationService] ❌ EXCEPTION in showBuyNowSuccessNotificationAfterWebView: $e');
      debugPrint('[NotificationService] ❌ Stack trace: $stackTrace');
    }
  }

  /// Show Buy Now success notification with custom context
  static void showBuyNowSuccessNotification(
    BuildContext rootContext, 
    ScaffoldMessengerState rootMessenger, 
    String orderId
  ) {
    try {
      debugPrint('[NotificationService] 📢 Showing Buy Now success notification');

      rootMessenger.clearSnackBars();
      rootMessenger.showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(rootContext).paymentSuccessful),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
        ),
      );

      debugPrint('[NotificationService] ✅ Buy Now success notification shown');
    } catch (e) {
      debugPrint('[NotificationService] ❌ Failed to show Buy Now success notification: $e');
    }
  }

  /// Handle Buy Now success after modal is already closed
  void handleBuyNowSuccessAfterModalClosed(String orderId) {
    try {
      debugPrint('[NotificationService] 🎉 Handling Buy Now success for order: $orderId (modal already closed)');

      // Schedule navigation operations to avoid conflicts
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _performBuyNowSuccessNavigationAfterModalClosed(orderId);
      });

    } catch (e) {
      debugPrint('[NotificationService] ❌ Error in Buy Now success handler: $e');
    }
  }

  /// Perform Buy Now success navigation operations after modal is already closed
  void _performBuyNowSuccessNavigationAfterModalClosed(String orderId) async {
    try {
      debugPrint('[NotificationService] 🔄 Performing Buy Now success navigation (modal already closed)');

      // Step 1: Show success notification first (before any navigation)
      if (context.mounted) {
        final rootContext = Navigator.of(context, rootNavigator: true).context;
        final rootMessenger = ScaffoldMessenger.of(rootContext);
        showBuyNowSuccessNotification(rootContext, rootMessenger, orderId);
      }

      // Step 2: Small delay to let notification show
      await Future.delayed(const Duration(milliseconds: 100));

      // Step 3: Navigate to home (modal already closed, so just navigate)
      if (context.mounted) {
        debugPrint('[NotificationService] 🏠 Navigating to home page after successful Buy Now');
        context.go('/home');
      }

    } catch (e) {
      debugPrint('[NotificationService] ❌ Error in Buy Now success navigation: $e');
    }
  }

  /// Show error notification with retry option
  void showErrorWithRetry(String message, VoidCallback onRetry) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: onRetry,
          ),
        ),
      );
    }
  }

  /// Show simple error notification
  void showError(String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Show simple success notification
  void showSuccess(String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}
