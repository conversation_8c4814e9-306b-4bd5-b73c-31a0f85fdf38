import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../auth/presentation/services/auth_guard_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/data/models/enhanced_user_model.dart';
import '../../../../core/utils/snackbar_utils.dart';

/// Service to handle authentication logic for purchase actions
/// Centralizes authentication checks and auth modal flows
class AuthenticationHandler {
  static const Duration _debounceDelay = Duration(seconds: 2);

  /// Check if user is authenticated for purchase actions
  static bool isUserAuthenticated(WidgetRef ref) {
    final authState = ref.read(authNotifierProvider);
    return authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && 
                 enhancedUser.backendToken != null && 
                 enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );
  }

  /// Handle Add to Cart authentication flow
  static Future<bool> handleAddToCartAuth({
    required BuildContext context,
    required WidgetRef ref,
    required DateTime? lastClick,
    required Function(DateTime?) onUpdateLastClick,
    required VoidCallback onExecute,
  }) async {
    // Debounce check
    final now = DateTime.now();
    if (lastClick != null && now.difference(lastClick) < _debounceDelay) {
      debugPrint('[AuthHandler] Add to Cart clicked too soon, ignoring (debounce)');
      return false;
    }
    onUpdateLastClick(now);

    debugPrint('[AuthHandler] Add to Cart button clicked');

    if (isUserAuthenticated(ref)) {
      debugPrint('[AuthHandler] User already authenticated, executing Add to Cart');
      onExecute();
      return true;
    } else {
      debugPrint('[AuthHandler] User not authenticated, showing auth modal');
      await _showAuthModal(
        context: context,
        ref: ref,
        isForCart: true,
        onSuccess: () {
          debugPrint('[AuthHandler] Authentication successful for Add to Cart');
          debugPrint('[AuthHandler] Modal remains open - user can now click Add to Cart when ready');
        },
        onError: (errorMessage) => _handleAuthError(context, errorMessage, 'Add to Cart'),
      );
      return false;
    }
  }

  /// Handle Buy Now authentication flow
  static Future<bool> handleBuyNowAuth({
    required BuildContext context,
    required WidgetRef ref,
    required DateTime? lastClick,
    required Function(DateTime?) onUpdateLastClick,
    required Future<void> Function() onExecute,
  }) async {
    // Debounce check
    final now = DateTime.now();
    if (lastClick != null && now.difference(lastClick) < _debounceDelay) {
      debugPrint('[AuthHandler] Buy Now clicked too soon, ignoring (debounce)');
      return false;
    }
    onUpdateLastClick(now);

    debugPrint('[AuthHandler] Buy Now button clicked');

    if (isUserAuthenticated(ref)) {
      debugPrint('[AuthHandler] User already authenticated, executing Buy Now');
      await onExecute();
      return true;
    } else {
      debugPrint('[AuthHandler] User not authenticated, showing auth modal');
      await _showAuthModal(
        context: context,
        ref: ref,
        isForCart: false,
        onSuccess: () {
          debugPrint('[AuthHandler] Authentication successful for Buy Now');
          debugPrint('[AuthHandler] Modal remains open - user can now click Buy Now when ready');
        },
        onError: (errorMessage) => _handleAuthError(context, errorMessage, 'Buy Now'),
      );
      return false;
    }
  }

  /// Show authentication modal
  static Future<void> _showAuthModal({
    required BuildContext context,
    required WidgetRef ref,
    required bool isForCart,
    required VoidCallback onSuccess,
    required Function(String) onError,
  }) async {
    if (isForCart) {
      await AuthGuardService.requireAuthForCart(
        context,
        ref,
        onSuccess: onSuccess,
        onError: onError,
      );
    } else {
      if (context.mounted) {
        await AuthGuardService.requireAuthForPurchase(
          context,
          ref,
          onSuccess: onSuccess,
          onError: onError,
        );
      }
    }

    // Check authentication state after modal returns
    final authStateAfter = ref.read(authNotifierProvider);
    final isAuthenticatedAfter = authStateAfter.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && 
                 enhancedUser.backendToken != null && 
                 enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      error: (errorState) {
        debugPrint('[AuthHandler] Authentication failed: ${errorState.failure.message}');
        return false;
      },
      orElse: () => false,
    );

    if (!isAuthenticatedAfter) {
      debugPrint('[AuthHandler] User returned from auth modal but is still not authenticated');
      debugPrint('[AuthHandler] Modal remains open - user can try authentication again or modify selection');
    }
  }

  /// Handle authentication errors with proper context management
  static void _handleAuthError(BuildContext context, String errorMessage, String action) {
    debugPrint('[AuthHandler] *** ERROR CALLBACK TRIGGERED *** Authentication failed for $action: $errorMessage');
    
    try {
      SnackbarUtils.showAuthError(context, errorMessage);
      debugPrint('[AuthHandler] ✅ Error SnackBar shown successfully');
    } catch (e) {
      debugPrint('[AuthHandler] ❌ Failed to show error SnackBar: $e');
      // Fallback: try with root context
      try {
        final rootContext = Navigator.of(context, rootNavigator: true).context;
        SnackbarUtils.showAuthError(rootContext, errorMessage);
        debugPrint('[AuthHandler] ✅ Error SnackBar shown with root context');
      } catch (e2) {
        debugPrint('[AuthHandler] ❌ Failed to show error SnackBar with root context: $e2');
      }
    }
  }
}
