import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../cart/presentation/providers/cart_provider.dart';
import '../../../cart/presentation/services/new_cart_checkout_service.dart';
import '../../../payment/presentation/screens/checkout_screen.dart';
import '../../../payment/presentation/providers/stripe_providers.dart';

import '../../../../core/services/remote_config_service.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/utils/shopify_utils.dart';


/// Service to handle Buy Now execution and payment processing
/// Centralizes the complex Buy Now flow including API calls and payment handling
class BuyNowService {
  final WidgetRef ref;
  final BuildContext context;
  final VoidCallback? onCloseModal;
  final Function(bool) onLoadingStateChanged;

  BuyNowService({
    required this.ref,
    required this.context,
    this.onCloseModal,
    required this.onLoadingStateChanged,
  });

  /// Execute Buy Now flow with comprehensive error handling
  Future<void> executeBuyNow({
    required String variantId,
    required String? productId,
    required int quantity,
    required double? price,
    required String? title,
    String? imageUrl,
  }) async {
    onLoadingStateChanged(true);
    
    try {
      debugPrint('[BuyNowService] Starting Buy Now execution - modal stays open');

      // Check remote config before making API call
      if (!_validateRemoteConfig()) {
        return;
      }

      // Buy now state is now handled by cart provider

      debugPrint('[BuyNowService] Using cart-based buy now flow');

      // Execute cart-based buy now flow
      await _executeCartBasedBuyNowFlow(
        variantId: variantId,
        productId: productId,
        quantity: quantity,
        price: price,
        title: title,
        imageUrl: imageUrl,
      );

    } catch (e) {
      debugPrint('[BuyNowService] Buy Now exception: $e');
      await _handleBuyNowError(e);
    } finally {
      onLoadingStateChanged(false);
    }
  }

  /// Validate remote config before proceeding
  bool _validateRemoteConfig() {
    try {
      RemoteConfigService.getBuyNowPaymentMethod();
      debugPrint('[BuyNowService] Remote Config check passed - proceeding with Buy Now');
      return true;
    } catch (e) {
      debugPrint('[BuyNowService] Remote Config failed before API call: $e');
      _handleRemoteConfigError();
      return false;
    }
  }

  /// Handle remote config errors
  void _handleRemoteConfigError() {
    // Close modal and navigate to home
    if (onCloseModal != null) {
      onCloseModal!();
    }

    // Navigate and show notification
    final navigatorContext = Navigator.of(context, rootNavigator: true).context;
    Future.delayed(const Duration(milliseconds: 100), () {
      if (navigatorContext.mounted) {
        GoRouter.of(navigatorContext).go('/home');
        Future.delayed(const Duration(milliseconds: 500), () {
          if (navigatorContext.mounted) {
            SnackbarUtils.showError(
              navigatorContext,
              'Payment setup required. Please contact support.',
              duration: const Duration(seconds: 4),
            );
          }
        });
      }
    });
  }

  /// Execute cart-based buy now flow
  Future<void> _executeCartBasedBuyNowFlow({
    required String variantId,
    required String? productId,
    required int quantity,
    required double? price,
    required String? title,
    String? imageUrl,
  }) async {
    if (!context.mounted) return;

    try {
      final success = await ref.read(cartProvider.notifier).executeBulkBuyNowOperation(
        variantId: variantId,
        productId: productId ?? '0',
        quantity: quantity,
        price: price ?? 0.0,
        title: title ?? 'Product',
        currency: 'USD',
        imageUrl: imageUrl,
      );

      if (!success) {
        throw Exception('Failed to execute bulk buy now operation');
      }

      await _triggerCartCheckoutForItem(variantId);

    } catch (e) {
      debugPrint('[BuyNowService] ❌ Cart-based buy now flow failed: $e');
      rethrow;
    }
  }



  /// Trigger cart checkout for the specific buy now item with dynamic routing
  Future<void> _triggerCartCheckoutForItem(String variantId) async {
    try {
      final numericVariantId = ShopifyUtils.extractVariantId(variantId);
      final paymentMethod = RemoteConfigService.getBuyNowPaymentMethod();

      if (paymentMethod.isShopify) {
        await _handleShopifyBuyNow(numericVariantId);
      } else {
        await _handleStripeBuyNow(numericVariantId);
      }
    } catch (e) {
      debugPrint('[BuyNowService] ❌ DYNAMIC ROUTING: Buy Now checkout failed: $e');
      rethrow;
    }
  }

  /// Handle Shopify Buy Now flow
  Future<void> _handleShopifyBuyNow(String numericVariantId) async {
    final cartCheckoutService = NewCartCheckoutService(
      context: context,
      ref: ref,
      isFromBuyNow: true,
    );

    await cartCheckoutService.handleSelectedItemsCheckout(
      selectedVariantIds: [numericVariantId],
      voucherCode: null,
    );
  }

  /// Handle Stripe Buy Now flow
  /// Validates Stripe configuration before navigation
  Future<void> _handleStripeBuyNow(String numericVariantId) async {
    if (!context.mounted) return;

    final cartState = ref.read(cartProvider);
    if (cartState.cart == null || cartState.cart!.items.isEmpty) {
      debugPrint('[BuyNowService] Cart is empty, cannot proceed with checkout');
      return;
    }

    final selectedItems = cartState.cart!.items
        .where((item) {
          final itemNumericId = ShopifyUtils.extractVariantId(item.variantId);
          return itemNumericId == numericVariantId;
        })
        .toList();

    if (selectedItems.isEmpty) {
      debugPrint('[BuyNowService] Buy now item not found in cart: $numericVariantId');
      return;
    }

    // Validate Stripe configuration before navigation
    debugPrint('[BuyNowService] 🔍 Validating Stripe configuration...');

    await ref.read(stripeInitializationProvider.notifier).initialize();

    debugPrint('[BuyNowService] ✅ Stripe initialization completed - proceeding to checkout');

    final totalPrice = selectedItems.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
    final itemsCount = selectedItems.fold(0, (sum, item) => sum + item.quantity);

    final filteredCart = cartState.cart!.copyWith(
      items: selectedItems,
      totalPrice: totalPrice,
      itemsCount: itemsCount,
    );

    debugPrint('[BuyNowService] 💳 DYNAMIC ROUTING: Filtered cart: ${filteredCart.items.length} items, total: \$${filteredCart.totalPrice.toStringAsFixed(2)}');
    debugPrint('[BuyNowService] 💳 DYNAMIC ROUTING: Selected variant ID: $numericVariantId');

    if (!context.mounted) return;

    // Navigate to CheckoutScreen with buy now context (same pattern as CartScreen)
    final route = MaterialPageRoute(
      builder: (context) => CheckoutScreen(
        cart: filteredCart,
        isFromBuyNow: true, // This is buy now checkout
      ),
    );

    debugPrint('[BuyNowService] 💳 DYNAMIC ROUTING: Navigating to CheckoutScreen...');
    await Navigator.of(context).push(route);

    // OPTIMIZATION: Skip cart refresh for buy now operations since the cart state
    // is already up-to-date from the bulk operation and checkout doesn't modify cart
    debugPrint('[BuyNowService] ✅ DYNAMIC ROUTING: Returned from CheckoutScreen (skipping cart refresh for buy now optimization)');
  }

  /// Handle Buy Now errors with proper cleanup
  Future<void> _handleBuyNowError(dynamic error) async {
    if (!context.mounted) return;

    // Close modal first
    if (onCloseModal != null) {
      onCloseModal!();
    }

    // Use post-frame callback for error handling
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        SnackbarUtils.showError(
          context,
          'An unexpected error occurred. Please try again.',
          duration: const Duration(seconds: 4),
        );
      }
    });
  }
}
