import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_stripe/flutter_stripe.dart'; // Add this import
import 'package:go_router/go_router.dart'; // Add this import for navigation

import '../../../cart/domain/entities/cart.dart';
import '../../../cart/presentation/providers/cart_provider.dart';
import '../../../cart/presentation/providers/cart_state.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/data/models/enhanced_user_model.dart';
import '../../../../core/utils/snackbar_utils.dart'; // Add this import
import '../../../../l10n/app_localizations.dart'; // Add this import for localization
import '../providers/stripe_providers.dart';
import '../providers/new_checkout_provider.dart';
import '../widgets/widgets.dart';
import '../../../purchase/domain/entities/buy_now_response.dart';

class CheckoutScreen extends ConsumerStatefulWidget {
  final Cart cart;
  final bool isFromBuyNow;
  final Map<String, dynamic>? buyNowData;
  final BuyNowResponse? existingPaymentIntent;

  const CheckoutScreen({
    super.key,
    required this.cart,
    this.isFromBuyNow = false,
    this.buyNowData,
    this.existingPaymentIntent,
  });

  @override
  ConsumerState<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends ConsumerState<CheckoutScreen> {
  // Track applied discounts and credits
  double _appliedCredits = 0.0;
  String? _appliedDiscountCode;
  double _discountAmount = 0.0;

  // Track loading state
  bool _isLoading = false;

  // Track card validation state
  bool _isCardFormValid = false;

  // Card fields removed - FigmaPaymentForm handles card input with Stripe CardField
  // FormType _selectedForm = FormType.custom; // Back to reliable CustomCardForm as default

  @override
  void initState() {
    super.initState();

    // Initialize Stripe and clear voucher states for clean checkout FIRST
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeStripe();
      _clearVoucherStatesForCleanCheckout();

      // THEN initialize voucher data from cart if available (only for cart checkout)
      if (!widget.isFromBuyNow && widget.cart.voucherCode != null) {
        // debugPrint('🎫 [CheckoutScreen] Cart checkout - initializing voucher from cart: ${widget.cart.voucherCode}');
        setState(() {
          _appliedDiscountCode = widget.cart.voucherCode;
          _discountAmount = widget.cart.discountAmount ?? 0.0;
        });
      } else if (widget.isFromBuyNow) {
        // debugPrint('🎫 [CheckoutScreen] Buy now checkout - starting with clean voucher state');
        setState(() {
          _appliedDiscountCode = null;
          _discountAmount = 0.0;
        });
      }
    });
  }

  Future<void> _initializeStripe() async {
    await ref.read(stripeInitializationProvider.notifier).initialize();
  }

  /// Clear voucher states to prevent cross-contamination between cart and buy now flows
  void _clearVoucherStatesForCleanCheckout() {
    debugPrint('🧹 [CheckoutScreen] Clearing voucher states for clean checkout...');
    debugPrint('🧹 [CheckoutScreen] Is from buy now: ${widget.isFromBuyNow}');

    if (widget.isFromBuyNow) {
      // For buy now flow, clear local state to ensure clean start
      debugPrint('🧹 [CheckoutScreen] Buy now flow - clearing all voucher states including local state');
      // Local state will be cleared in initState after this method
    } else {
      // For cart flow, keep cart voucher data
      debugPrint('🧹 [CheckoutScreen] Cart flow - cleared buy now voucher state, will preserve cart voucher data');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen to cart state changes for real-time updates (only for cart checkout)
    if (!widget.isFromBuyNow) {
      ref.listen<CartState>(cartProvider, (previous, next) {
        if (next.cart != null) {
          final currentCart = next.cart!;
          if (currentCart.voucherCode != _appliedDiscountCode ||
              currentCart.discountAmount != _discountAmount) {
            if (mounted) {
              setState(() {
                _appliedDiscountCode = currentCart.voucherCode;
                _discountAmount = currentCart.discountAmount ?? 0.0;
              });
            }
          }
        }
      });
    }

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: const Color(0xFFF3F4F6),
        body: Column(
          children: [
            _buildHeader(context),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildProductCard(),
                    _buildPaymentSection(),
                    _buildCreditsAndDiscountSection(),
                    // Bottom section as part of scrollable content to prevent overflow
                    _buildBottomSection(_isLoading),
                    // Add safe area padding at the bottom
                    SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildProductCard() {
    // Always use the cart passed to this screen (which may be filtered for selected items only)
    // Don't use cart from provider as it contains all items, not just selected ones
    final currentCart = widget.cart;

    // Show all items in the provided cart (which should be filtered for selected items)
    return Column(
      children: currentCart.items.map((item) =>
        CheckoutProductCard(item: item)
      ).toList(),
    );
  }

  Widget _buildPaymentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Payment header
        const PaymentHeaderWidget(),

        // Form comparison toggle
        // Container(
        //   padding: const EdgeInsets.all(12),
        //   margin: const EdgeInsets.only(bottom: 16),
        //   decoration: BoxDecoration(
        //     color: Colors.blue.withOpacity(0.1),
        //     borderRadius: BorderRadius.circular(8),
        //     border: Border.all(color: Colors.blue.withOpacity(0.3)),
        //   ),
        //   child: Column(
        //     crossAxisAlignment: CrossAxisAlignment.start,
        //     children: [
        //       Text(
        //         '🌍 Country Support Comparison',
        //         style: TextStyle(
        //           fontSize: 16,
        //           fontWeight: FontWeight.bold,
        //           color: Colors.blue[800],
        //         ),
        //       ),
        //       const SizedBox(height: 8),
        //       // Four form options in 2x2 grid
        //       Column(
        //         children: [
        //           Row(
        //             children: [
        //               Expanded(
        //                 child: GestureDetector(
        //                   onTap: () => setState(() => _selectedForm = FormType.custom),
        //                   child: Container(
        //                     padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        //                     decoration: BoxDecoration(
        //                       color: _selectedForm == FormType.custom ? Colors.blue : Colors.transparent,
        //                       borderRadius: BorderRadius.circular(6),
        //                       border: Border.all(
        //                         color: _selectedForm == FormType.custom ? Colors.blue : Colors.grey,
        //                       ),
        //                     ),
        //                     child: Text(
        //                       'CustomCardForm\n(66 Countries)',
        //                       textAlign: TextAlign.center,
        //                       style: TextStyle(
        //                         color: _selectedForm == FormType.custom ? Colors.white : Colors.grey[700],
        //                         fontSize: 11,
        //                         fontWeight: FontWeight.w500,
        //                       ),
        //                     ),
        //                   ),
        //                 ),
        //               ),
        //               const SizedBox(width: 8),
        //               Expanded(
        //                 child: GestureDetector(
        //                   onTap: () => setState(() => _selectedForm = FormType.stripe),
        //                   child: Container(
        //                     padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        //                     decoration: BoxDecoration(
        //                       color: _selectedForm == FormType.stripe ? Colors.green : Colors.transparent,
        //                       borderRadius: BorderRadius.circular(6),
        //                       border: Border.all(
        //                         color: _selectedForm == FormType.stripe ? Colors.green : Colors.grey,
        //                       ),
        //                     ),
        //                     child: Text(
        //                       'CardFormField\n(All Stripe)',
        //                       textAlign: TextAlign.center,
        //                       style: TextStyle(
        //                         color: _selectedForm == FormType.stripe ? Colors.white : Colors.grey[700],
        //                         fontSize: 11,
        //                         fontWeight: FontWeight.w500,
        //                       ),
        //                     ),
        //                   ),
        //                 ),
        //               ),
        //             ],
        //           ),
        //           const SizedBox(height: 8),
        //           Row(
        //             children: [
        //               Expanded(
        //                 child: GestureDetector(
        //                   onTap: () => setState(() => _selectedForm = FormType.enhanced),
        //                   child: Container(
        //                     padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        //                     decoration: BoxDecoration(
        //                       color: _selectedForm == FormType.enhanced ? Colors.purple : Colors.transparent,
        //                       borderRadius: BorderRadius.circular(6),
        //                       border: Border.all(
        //                         color: _selectedForm == FormType.enhanced ? Colors.purple : Colors.grey,
        //                       ),
        //                     ),
        //                     child: Text(
        //                       'Enhanced\n(Libraries)',
        //                       textAlign: TextAlign.center,
        //                       style: TextStyle(
        //                         color: _selectedForm == FormType.enhanced ? Colors.white : Colors.grey[700],
        //                         fontSize: 11,
        //                         fontWeight: FontWeight.w500,
        //                       ),
        //                     ),
        //                   ),
        //                 ),
        //               ),
        //               const SizedBox(width: 8),
        //               Expanded(
        //                 child: GestureDetector(
        //                   onTap: () => setState(() => _selectedForm = FormType.css),
        //                   child: Container(
        //                     padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        //                     decoration: BoxDecoration(
        //                       color: _selectedForm == FormType.css ? Colors.orange : Colors.transparent,
        //                       borderRadius: BorderRadius.circular(6),
        //                       border: Border.all(
        //                         color: _selectedForm == FormType.css ? Colors.orange : Colors.grey,
        //                       ),
        //                     ),
        //                     child: Text(
        //                       'CSS WebView\n(Real CSS)',
        //                       textAlign: TextAlign.center,
        //                       style: TextStyle(
        //                         color: _selectedForm == FormType.css ? Colors.white : Colors.grey[700],
        //                         fontSize: 11,
        //                         fontWeight: FontWeight.w500,
        //                       ),
        //                     ),
        //                   ),
        //                 ),
        //               ),
        //             ],
        //           ),
        //         ],
        //       ),
        //     ],
        //   ),
        // ),

        // Show selected form
        FigmaPaymentForm(
          key: const ValueKey('checkout_payment_form'), // Prevent recreation
          onValidationChanged: (isValid) {
            // debugPrint('FigmaPaymentForm validation: $isValid');
            if (mounted) {
              setState(() {
                _isCardFormValid = isValid;
              });
            }
          },
        ),

        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildBottomSection(bool isLoading) {
    // Always use the cart passed to this screen (which may be filtered for selected items only)
    // Don't use cart from provider as it contains all items, not just selected ones
    final currentCart = widget.cart;

    // Calculate pricing breakdown using actual applied values
    // For buy now, use actual product price; for cart, use cart total
    final originalPrice = widget.isFromBuyNow && currentCart.items.isNotEmpty
        ? currentCart.items.first.price * currentCart.items.first.quantity
        : currentCart.totalPrice;
    final creditsDiscount = _appliedCredits;

    // Use different voucher data based on checkout type
    double voucherDiscount;
    String? voucherCode;

    // Use cart voucher data or local discount for both buy now and cart flows
    voucherDiscount = currentCart.discountAmount ?? _discountAmount;
    voucherCode = currentCart.voucherCode ?? _appliedDiscountCode;

    final finalPrice = currentCart.discountedTotal ?? (originalPrice - creditsDiscount - voucherDiscount);

    return Container(
      width: double.infinity, // Full width - responsive
      constraints: const BoxConstraints(minHeight: 140), // Reduced minimum height to prevent overflow
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF), // Exact Figma white
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E7EB), // Exact Figma border color
            width: 1,
          ),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 24), // Reduced bottom padding from 32 to 24
        child: Column(
          children: [
            // Grand Total section with price breakdown
            Row(
              crossAxisAlignment: CrossAxisAlignment.start, // Align to top for multi-line content
              children: [
                // Grand Total label section - exact Figma layout
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(8, 8, 16, 8),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Grand Total:',
                            style: GoogleFonts.inter(
                              fontSize: 18, // Exact Figma font size
                              fontWeight: FontWeight.w600, // Exact Figma font weight
                              color: const Color(0xFF1F2937), // Exact Figma color
                              height: 1.2102272245619032, // Exact Figma line height
                            ),
                            textAlign: TextAlign.right, // Right-aligned text
                            maxLines: 1, // Prevent line breaking
                            overflow: TextOverflow.ellipsis, // Handle overflow gracefully
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Vertical divider line - flexible height to match content
                Container(
                  width: 1, // Exact Figma width (0 in design but 1 for visibility)
                  constraints: const BoxConstraints(minHeight: 23), // Minimum height, can expand
                  color: const Color(0xFFE5E7EB), // Exact Figma stroke color
                ),

                // Price breakdown section - dynamic height for multiple lines
                Expanded(
                  child: Container(
                    constraints: const BoxConstraints(minHeight: 46), // Minimum height, can expand
                    padding: const EdgeInsets.all(12), // Exact Figma padding
                    decoration: const BoxDecoration(
                      color: Color(0xFFFFFFFF), // Exact Figma background
                      borderRadius: BorderRadius.all(Radius.circular(8)), // Exact Figma border radius
                    ),
                    child: Center(
                      child: _buildPriceBreakdownWidget(originalPrice, creditsDiscount, voucherDiscount, finalPrice, voucherCode),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16), // Exact Figma gap

            // Pay now button
            SizedBox(
              width: double.infinity,
              child: Builder(
                builder: (context) {
                  final canProceed = _canProceedWithPayment();
                  // debugPrint('[PayNow] Button state - isLoading: $isLoading, canProceed: $canProceed');
                  // debugPrint('[PayNow] Button enabled: ${!isLoading && canProceed}');
                  // debugPrint('[PayNow] Card form valid: $_isCardFormValid');

                  return ElevatedButton(
                    onPressed: (isLoading || !canProceed) ? null : () {
                      // debugPrint('🔥🔥🔥 [PayNow] Button actually pressed!');
                      _handlePayment();
                    },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6982), // Exact Figma button color
                  foregroundColor: const Color(0xFFFFFFFF), // Exact Figma text color
                  elevation: 4, // Figma shadow effect
                  shadowColor: const Color.fromRGBO(0, 0, 0, 0.25), // Exact Figma shadow
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6), // Exact Figma border radius
                  ),
                  padding: const EdgeInsets.all(12), // Exact Figma padding
                  minimumSize: const Size(double.infinity, 48), // Ensure minimum height
                ),
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Pay now', // Exact Figma text
                        style: GoogleFonts.inter(
                          fontSize: 14, // Exact Figma font size
                          fontWeight: FontWeight.w500, // Exact Figma font weight
                          letterSpacing: 0.005, // 0.5% letter spacing as per Figma
                          height: 1.4285714285714286, // Exact Figma line height
                        ),
                        textAlign: TextAlign.center, // Center-aligned as per Figma
                      ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }



  void _handlePayment() async {
    debugPrint('🚀🚀🚀 [DirectPayment] _handlePayment() method called!');
    debugPrint('🚀 [DirectPayment] Button pressed');
    debugPrint('🚀 [DirectPayment] Cart total: \$${widget.cart.totalPrice.toStringAsFixed(2)}');
    debugPrint('🚀 [DirectPayment] Widget mounted: $mounted');
    debugPrint('🚀 [DirectPayment] Is loading: $_isLoading');

    // Show loading state
    setState(() {
      _isLoading = true;
    });

    try {
      // Check if there's already a payment in progress
      final currentState = ref.read(newCheckoutProvider);
      if (currentState.isLoading) {
        debugPrint('⚠️ [DirectPayment] Payment already in progress, ignoring duplicate request');
        return;
      }

      // Reset any previous checkout state to ensure fresh payment intent
      debugPrint('🚀 [DirectPayment] Resetting previous checkout state...');
      ref.read(newCheckoutProvider.notifier).reset();

      // Create NEW payment intent using appropriate flow
      debugPrint('🚀 [DirectPayment] Creating NEW payment intent...');
      debugPrint('🚀 [DirectPayment] Is from buy now: ${widget.isFromBuyNow}');
      final checkoutNotifier = ref.read(newCheckoutProvider.notifier);

      if (widget.isFromBuyNow && widget.existingPaymentIntent != null) {
        // Use existing payment intent from buy now flow to avoid duplicate API calls
        debugPrint('🚀 [DirectPayment] Using existing payment intent from buy now flow');
        debugPrint('🚀 [DirectPayment] Payment Intent ID: ${widget.existingPaymentIntent!.paymentIntentId}');
        debugPrint('🚀 [DirectPayment] Client Secret: ${widget.existingPaymentIntent!.clientSecret}');

        // Set the existing payment intent in the checkout provider
        checkoutNotifier.setExistingPaymentIntent(
          paymentIntentId: widget.existingPaymentIntent!.paymentIntentId!,
          clientSecret: widget.existingPaymentIntent!.clientSecret!,
          orderId: widget.existingPaymentIntent!.orderId,
        );
      } else if (widget.isFromBuyNow && widget.cart.items.isNotEmpty) {
        // Fallback: Use buy now flow for direct purchases (when no existing payment intent)
        final item = widget.cart.items.first;
        debugPrint('🚀 [DirectPayment] Using buy now flow for variant: ${item.variantId}');
        await checkoutNotifier.createBuyNowPaymentIntent(
          variantId: item.variantId,
          quantity: item.quantity,
          price: item.price,
          productName: item.title,
        );
      } else {
        // Use cart flow for regular cart checkout
        debugPrint('🚀 [DirectPayment] Using cart flow');
        await checkoutNotifier.createCartPaymentIntent(widget.cart);
      }
      
      // Check if NEW payment intent was created successfully
      final checkoutState = ref.read(newCheckoutProvider);
      if (checkoutState.paymentIntent == null) {
        throw Exception('Failed to create NEW payment intent');
      }

      final clientSecret = checkoutState.paymentIntent!.clientSecret;
      final paymentIntentId = checkoutState.paymentIntent!.paymentIntentId;
      debugPrint('🚀 [DirectPayment] ✅ NEW payment intent created successfully: $paymentIntentId');
      debugPrint('🚀 [DirectPayment] Using client secret: ${clientSecret.substring(0, 20)}...');

      // Confirm payment and handle success
      await _confirmPaymentAndPoll(clientSecret, checkoutNotifier);

    } on StripeException catch (e) {
      _handleStripeError(e);
    } catch (e) {
      _handleGeneralError(e);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }



  /// Build price breakdown widget with styled text for discounts
  Widget _buildPriceBreakdownWidget(double originalPrice, double creditsDiscount, double voucherDiscount, double finalPrice, String? voucherCode) {
    // Check if any discounts are applied
    bool hasCredits = creditsDiscount > 0;
    bool hasVoucher = voucherDiscount > 0 && voucherCode != null && voucherCode.isNotEmpty;
    bool hasAnyDiscount = hasCredits || hasVoucher;

    // Get enhanced discount information
    String? discountTitle;
    bool isShopifyDiscount = false;

    // Use cart state for both buy now and cart flows
    final cartState = ref.watch(cartProvider);
    discountTitle = cartState.discountTitle;
    isShopifyDiscount = cartState.hasShopifyDiscount;

    // If no discounts applied, show only the original price
    if (!hasAnyDiscount) {
      return Text(
        '\$${originalPrice.toStringAsFixed(2)}',
        style: GoogleFonts.inter(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF1F2937),
          height: 1.2102272245619032,
        ),
        textAlign: TextAlign.right,
      );
    }

    // If discounts are applied, show the styled breakdown
    List<Widget> children = [];

    // Original price with strikethrough when voucher applied
    children.add(
      Text(
        '\$${originalPrice.toStringAsFixed(2)}',
        style: GoogleFonts.inter(
          fontSize: hasVoucher ? 14 : 18, // Nhỏ hơn khi có voucher (14px), bình thường khi không có (18px)
          fontWeight: hasVoucher ? FontWeight.w500 : FontWeight.w600, // Lighter weight khi có voucher
          color: hasVoucher ? const Color(0xFF6B7280) : const Color(0xFF1F2937), // Màu nhạt hơn khi có voucher
          height: 1.2102272245619032,
          decoration: hasVoucher ? TextDecoration.lineThrough : null, // Gạch ngang khi có voucher
          decorationColor: const Color(0xFF6B7280), // Màu gạch ngang
        ),
        textAlign: TextAlign.right,
      ),
    );

    // Credits discount (if applied)
    if (hasCredits) {
      children.add(
        Text(
          'Credits (-\$${creditsDiscount.toStringAsFixed(2)})',
          style: GoogleFonts.inter(
            fontSize: 14, // Smaller font for discount lines
            fontWeight: FontWeight.w500,
            color: const Color(0xFF059669), // Green color for credits
            height: 1.2102272245619032,
          ),
          textAlign: TextAlign.right,
        ),
      );
    }

    // Voucher discount (if applied) - enhanced with title and type
    if (hasVoucher) {
      // Show discount title if available, otherwise show voucher code
      String displayText = discountTitle ?? voucherCode.toUpperCase();

      children.add(
        Text(
          '$displayText (-\$${voucherDiscount.toStringAsFixed(2)})',
          style: GoogleFonts.inter(
            fontSize: 14, // Smaller font for voucher code
            fontWeight: FontWeight.w500,
            color: const Color(0xFFFF6982), // Pink color for voucher
            height: 1.2102272245619032,
          ),
          textAlign: TextAlign.right,
        ),
      );

      // Show voucher code separately if we displayed the title
      if (discountTitle != null && discountTitle != voucherCode) {
        children.add(
          Text(
            'Code: ${voucherCode.toUpperCase()}',
            style: GoogleFonts.inter(
              fontSize: 12, // Even smaller for code
              fontWeight: FontWeight.w400,
              color: const Color(0xFF6B7280), // Gray color for code
              height: 1.2102272245619032,
            ),
            textAlign: TextAlign.right,
          ),
        );
      }

      // Show discount type indicator for Shopify discounts
      if (isShopifyDiscount) {
        children.add(
          Text(
            'Shopify Discount',
            style: GoogleFonts.inter(
              fontSize: 10, // Very small for type indicator
              fontWeight: FontWeight.w500,
              color: const Color(0xFF059669), // Green color for Shopify
              height: 1.2102272245619032,
            ),
            textAlign: TextAlign.right,
          ),
        );
      }
    }

    // Final price - prominent
    children.add(
      Text(
        '\$  ${finalPrice.toStringAsFixed(2)}',
        style: GoogleFonts.inter(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF1F2937),
          height: 1.2102272245619032,
        ),
        textAlign: TextAlign.right,
      ),
    );

    debugPrint('[CheckoutScreen] Price breakdown with discounts displayed');
    debugPrint('[CheckoutScreen] Voucher: $voucherCode, Discount: \$${voucherDiscount.toStringAsFixed(2)}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  /// Check if payment can proceed based on validation rules
  bool _canProceedWithPayment() {
    // Check if cart has items
    if (widget.cart.items.isEmpty) {
      // debugPrint('[PayNow] ❌ Cart is empty');
      return false;
    }

    // Check if cart total is valid (greater than 0)
    if (widget.cart.totalPrice <= 0) {
      // debugPrint('[PayNow] ❌ Cart total is invalid: ${widget.cart.totalPrice}');
      return false;
    }

    // Check if card form is valid
    // debugPrint('[PayNow] 🔍 Card form validation: _isCardFormValid = $_isCardFormValid');
    if (!_isCardFormValid) {
      // debugPrint('[PayNow] ❌ Card form is invalid');
      return false;
    }

    // Check authentication state
    final authState = ref.read(authNotifierProvider);
    // debugPrint('[PayNow] Auth state: $authState');

    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          final hasAuth = enhancedUser.hasBackendAuth &&
                 enhancedUser.backendToken != null &&
                 enhancedUser.backendToken!.isNotEmpty;
          // debugPrint('[PayNow] Backend auth check: hasBackendAuth=${enhancedUser.hasBackendAuth}, hasToken=${enhancedUser.backendToken != null}');
          return hasAuth;
        }
        // debugPrint('[PayNow] User is not EnhancedUserModel');
        return false;
      },
      orElse: () {
        // debugPrint('[PayNow] User is not authenticated');
        return false;
      },
    );

    if (!hasBackendAuth) {
      // debugPrint('[PayNow] ❌ Backend authentication failed');
      return false;
    }

    // Check Stripe initialization
    final stripeInitState = ref.read(stripeInitializationProvider);
    // debugPrint('[PayNow] Stripe state: isLoading=${stripeInitState.isLoading}, error=${stripeInitState.error}, isInitialized=${stripeInitState.isInitialized}');

    if (stripeInitState.isLoading) {
      // debugPrint('[PayNow] ❌ Stripe is still loading');
      return false;
    }

    if (stripeInitState.error != null && !stripeInitState.isInitialized) {
      // debugPrint('[PayNow] ❌ Stripe initialization failed, attempting retry...');
      // Trigger a retry in the background
      Future.microtask(() {
        ref.read(stripeInitializationProvider.notifier).forceInitialize();
      });
      return false;
    }

    if (!stripeInitState.isInitialized) {
      // debugPrint('[PayNow] ❌ Stripe not initialized');
      return false;
    }

    // All validations passed
    // debugPrint('[PayNow] ✅ All validations passed - button should be enabled');
    return true;
  }

  /// Handle successful checkout completion - same pattern as cart checkout
  void _handleCheckoutSuccess(String? orderId) {
    // debugPrint('🎉 [CheckoutScreen] _handleCheckoutSuccess called!');
    // debugPrint('🎉 [CheckoutScreen] Checkout completed successfully, navigating to home');
    // debugPrint('🎉 [CheckoutScreen] Order ID: $orderId');

    try {
      // Clear voucher states after successful checkout to prevent cross-contamination
      debugPrint('🎉 [CheckoutScreen] Clearing voucher states after successful checkout...');

      // Voucher state is now handled by cart provider

      // Refresh cart after successful checkout (in case it was a cart checkout)
      debugPrint('🎉 [CheckoutScreen] Refreshing cart...');
      ref.read(cartProvider.notifier).getCart();

      // Capture root context before navigation to avoid disposal issues
      debugPrint('🎉 [CheckoutScreen] Getting root context...');
      final rootContext = Navigator.of(context, rootNavigator: true).context;
      final rootMessenger = ScaffoldMessenger.of(rootContext);
      final router = GoRouter.of(rootContext);

      // For buy now flow, close the checkout screen first, then navigate to home
      if (widget.isFromBuyNow) {
        debugPrint('🎉 [CheckoutScreen] Buy now flow - closing checkout screen first...');
        Navigator.of(context).pop(); // Close checkout screen

        // Navigate to home immediately and show notification
        debugPrint('🎉 [CheckoutScreen] Navigating to home after closing checkout...');
        router.go('/');

        // Show success notification on home screen
        debugPrint('🎉 [CheckoutScreen] Showing success notification...');
        _showSuccessNotification(rootContext, rootMessenger, orderId);
      } else {
        // For cart flow, navigate directly to home
        debugPrint('🎉 [CheckoutScreen] Cart flow - navigating to home...');
        router.go('/');

        // Show success notification on home screen
        debugPrint('🎉 [CheckoutScreen] Showing success notification...');
        _showSuccessNotification(rootContext, rootMessenger, orderId);
      }

      debugPrint('🎉 [CheckoutScreen] Success flow completed!');
    } catch (e) {
      debugPrint('❌ [CheckoutScreen] Error in success handler: $e');
    }
  }

  /// Show success notification on home screen - same pattern as cart checkout
  void _showSuccessNotification(BuildContext rootContext, ScaffoldMessengerState rootMessenger, String? orderId) {
    debugPrint('[CheckoutScreen] Showing success notification on home screen');
    try {
      final l10n = AppLocalizations.of(rootContext);
      rootMessenger.clearSnackBars();
      rootMessenger.showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.paymentSuccessful,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          margin: EdgeInsets.all(16),
        ),
      );
      debugPrint('[CheckoutScreen] ✅ Success notification shown successfully');
    } catch (e) {
      debugPrint('[CheckoutScreen] ❌ Failed to show success notification: $e');
    }
  }

  /// Build credits and discount section
  Widget _buildCreditsAndDiscountSection() {
    // Get current cart from provider
    final cartState = ref.watch(cartProvider);
    final currentCart = cartState.cart ?? widget.cart;

    return Column(
      children: [
        // const SizedBox(height: 7),

        // Available credits section
        WalletCreditsWidget(
          availableCredits: 0.00, // TODO: Get from user profile/wallet
          onCreditsApplied: (amount) {
            setState(() {
              _appliedCredits = amount;
            });
            debugPrint('Credits applied: \$${amount.toStringAsFixed(2)}');
          },
        ),

        const SizedBox(height: 7),

        // Discount code section - simplified to use cart-based voucher handling
        Consumer(
          builder: (context, ref, child) {
            final cartState = ref.watch(cartProvider);
            final orderAmount = widget.cart.items.isNotEmpty
                ? widget.cart.items.first.price * widget.cart.items.first.quantity
                : widget.cart.totalPrice;

            return Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text('Discount Code'),
                  // Simplified discount handling - voucher functionality through cart provider
                  if (cartState.cart?.voucherCode != null)
                    Text('Applied: ${cartState.cart!.voucherCode}'),
                ],
              ),
            );
          },
        ),

        const SizedBox(height: 7),
      ],
    );
  }

  /// Confirm payment with Stripe and poll backend for verification
  Future<void> _confirmPaymentAndPoll(String clientSecret, dynamic checkoutNotifier) async {
    // debugPrint('💳 [DirectPayment] Confirming payment intent directly...');

    try {
      // First, try to retrieve the payment intent to check its current status
      debugPrint('🔍 [DirectPayment] Checking payment intent status before confirmation...');

      // Confirm payment using CardFormField data
      final paymentIntent = await Stripe.instance.confirmPayment(
        paymentIntentClientSecret: clientSecret,
        data: const PaymentMethodParams.card(
          paymentMethodData: PaymentMethodData(),
        ),
      );

      // Check payment status
      if (paymentIntent.status == PaymentIntentsStatus.Succeeded) {
        debugPrint('✅ [DirectPayment] Payment completed successfully!');
        debugPrint('✅ [DirectPayment] CardFormField provided card data directly');

        // Poll backend to verify payment status
        await _pollBackendForVerification(checkoutNotifier);
      } else {
        throw Exception('Payment not completed: ${paymentIntent.status}');
      }
    } on StripeException catch (e) {
      // Handle case where payment intent is already confirmed
      final errorMessage = e.error.message?.toLowerCase() ?? '';
      final isAlreadyConfirmed = e.error.code == FailureCode.Failed &&
          (errorMessage.contains('đã thành công sau khi đã được xác nhận trước đó') ||
           errorMessage.contains('already succeeded') ||
           errorMessage.contains('payment_intent_unexpected_state') ||
           e.error.stripeErrorCode == 'payment_intent_unexpected_state');

      if (isAlreadyConfirmed) {
        debugPrint('🎯 [DirectPayment] Payment intent already confirmed - treating as success');
        debugPrint('🎯 [DirectPayment] Error details: ${e.error.message}');
        debugPrint('🎯 [DirectPayment] Proceeding to backend verification...');

        // Payment was already successful, proceed to backend verification
        await _pollBackendForVerification(checkoutNotifier);
      } else {
        // Re-throw other Stripe errors to be handled by the main error handler
        rethrow;
      }
    }
  }

  /// Poll backend for payment verification
  Future<void> _pollBackendForVerification(dynamic checkoutNotifier) async {
    debugPrint('🔍 [DirectPayment] Polling backend to verify payment status...');

    try {
      await checkoutNotifier.pollPaymentStatus();

      final finalState = ref.read(newCheckoutProvider);
      debugPrint('🔍 [DirectPayment] Final state after polling: ${finalState.paymentStatus?.status}');
      debugPrint('🔍 [DirectPayment] Is successful: ${finalState.paymentStatus?.isSuccessful}');

      if (finalState.paymentStatus?.isSuccessful == true) {
        debugPrint('✅ [DirectPayment] Backend confirmed payment success!');
        debugPrint('✅ [DirectPayment] Order completed: ${finalState.paymentIntent?.orderId}');

        if (mounted) {
          _handleCheckoutSuccess(finalState.paymentIntent?.orderId);
        } else {
          debugPrint('❌ [DirectPayment] Widget not mounted, cannot call success handler');
        }
      } else {
        debugPrint('❌ [DirectPayment] Payment not successful: ${finalState.paymentStatus?.status}');
        throw Exception('Backend payment verification failed: ${finalState.paymentStatus?.status}');
      }
    } catch (pollingError) {
      debugPrint('❌ [DirectPayment] Backend polling failed: $pollingError');

      // Check if this is a backend cart_id error (payment succeeded but backend has issues)
      if (pollingError.toString().contains('cart_id')) {
        debugPrint('🎯 [DirectPayment] Backend cart_id error detected - payment likely succeeded');
        debugPrint('🎯 [DirectPayment] Treating as success and redirecting to home');

        if (mounted) {
          _handleCheckoutSuccess('pending-backend-fix');
        }
        return; // Don't throw error, treat as success
      }

      throw Exception('Payment succeeded but order completion failed: $pollingError');
    }
  }

  /// Handle Stripe-specific errors
  void _handleStripeError(StripeException e) {
    debugPrint('❌ [DirectPayment] Stripe error: ${e.error}');
    debugPrint('❌ [DirectPayment] Error code: ${e.error.code}');
    debugPrint('❌ [DirectPayment] Error message: ${e.error.message}');
    debugPrint('❌ [DirectPayment] Localized message: ${e.error.localizedMessage}');

    if (mounted) {
      final l10n = AppLocalizations.of(context);
      String errorMessage;

      // Handle specific Stripe error codes
      switch (e.error.code) {
        case FailureCode.Canceled:
          errorMessage = l10n.paymentCanceled;
          break;
        case FailureCode.Failed:
          // Use localized message for card declined errors
          errorMessage = e.error.localizedMessage ?? e.error.message ?? l10n.paymentFailed;
          break;
        default:
          errorMessage = e.error.localizedMessage ?? e.error.message ?? l10n.paymentErrorUnknown;
      }

      SnackbarUtils.showPaymentError(
        context,
        errorMessage,
        onRetry: () => _handlePayment(),
      );
    }
  }

  /// Handle general errors
  void _handleGeneralError(dynamic e) {
    debugPrint('❌ [DirectPayment] Payment failed: $e');

    if (mounted) {
      final l10n = AppLocalizations.of(context);
      String errorMessage = l10n.paymentFailed;

      // Extract meaningful error message and determine if retry should be allowed
      bool allowRetry = true;

      if (e.toString().contains('Failed to create payment intent')) {
        errorMessage = l10n.paymentIntentCreationFailed;
      } else if (e.toString().contains('Backend payment verification failed')) {
        errorMessage = l10n.paymentVerificationFailed;
        allowRetry = false; // Backend issue, don't allow retry
      } else if (e.toString().contains('Payment succeeded but order completion failed')) {
        errorMessage = l10n.paymentSucceededOrderFailed;
        allowRetry = false; // Backend issue, don't allow retry
      } else if (e.toString().contains('cart_id')) {
        errorMessage = l10n.backendSystemError;
        allowRetry = false; // Backend implementation issue, don't allow retry
      }

      SnackbarUtils.showPaymentError(
        context,
        errorMessage,
        onRetry: allowRetry ? () => _handlePayment() : null,
      );
    }
  }

  /// Build header section with navigation and title
  Widget _buildHeader(BuildContext context) {
    return Container(
      color: const Color(0xFFFF6982),
      child: SafeArea(
        bottom: false,
        child: Container(
          height: 42,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
              ),
              Expanded(
                child: Text(
                  widget.isFromBuyNow ? 'Buy Now Checkout' : 'Check out',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => context.go('/'),
                child: const Icon(Icons.close, color: Colors.white, size: 20),
              ),
            ],
          ),
        ),
      ),
    );
  }

}
