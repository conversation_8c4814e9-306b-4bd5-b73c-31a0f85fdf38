import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_travelgator/core/network/api_config.dart';

/// Service for the new unified order creation flow
/// Implements POST /api/orders endpoint
class UnifiedOrderService {
  final Dio _dio;

  UnifiedOrderService({Dio? dio}) : _dio = dio ?? Dio(BaseOptions(
    baseUrl: ApiConfig.baseUrl,
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
  ));

  /// Create order using the new POST /api/orders endpoint
  /// This replaces the old POST /api/checkout_items flow
  Future<UnifiedOrderResponse> createOrder({
    required String paymentMethod,
    required String authToken,
  }) async {
    try {
      debugPrint('[UnifiedOrderService] *** ENTRY POINT *** createOrder() called');
      debugPrint('[UnifiedOrderService] Creating order with payment method: $paymentMethod');
      debugPrint('[UnifiedOrderService] About to make POST /api/orders call...');

      final response = await _dio.post(
        '/api/orders',
        data: {
          'payment_method': paymentMethod,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[UnifiedOrderService] Order creation response: ${response.statusCode}');
      debugPrint('[UnifiedOrderService] Response data: ${response.data}');

      if (response.statusCode == 200) {
        return UnifiedOrderResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to create order: ${response.statusCode}');
      }
    } on DioException catch (e) {
      debugPrint('[UnifiedOrderService] ❌ Dio error creating order: ${e.message}');
      debugPrint('[UnifiedOrderService] ❌ Response data: ${e.response?.data}');
      throw Exception('Order creation failed: ${e.message}');
    } catch (e) {
      debugPrint('[UnifiedOrderService] ❌ Unexpected error creating order: $e');
      rethrow;
    }
  }
}

/// Response model for the new POST /api/orders endpoint
class UnifiedOrderResponse {
  final bool success;
  final String? orderId;
  final String? paymentIntentId;
  final String? clientSecret;
  final String? checkoutUrl;
  final double? amount;
  final String? currency;
  final String? status;
  final bool? voucherApplied;
  final double? discountAmount;
  final String? message;

  UnifiedOrderResponse({
    required this.success,
    this.orderId,
    this.paymentIntentId,
    this.clientSecret,
    this.checkoutUrl,
    this.amount,
    this.currency,
    this.status,
    this.voucherApplied,
    this.discountAmount,
    this.message,
  });

  factory UnifiedOrderResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>?;
    
    return UnifiedOrderResponse(
      success: json['success'] ?? false,
      orderId: data?['order_id'],
      paymentIntentId: data?['payment_intent_id'],
      clientSecret: data?['client_secret'],
      checkoutUrl: data?['checkout_url'],
      amount: (data?['amount'] as num?)?.toDouble(),
      currency: data?['currency'],
      status: data?['status'],
      voucherApplied: data?['voucher_applied'],
      discountAmount: (data?['discount_amount'] as num?)?.toDouble(),
      message: json['message'],
    );
  }

  /// Check if this is a Stripe payment response
  bool get isStripePayment => clientSecret != null && paymentIntentId != null;

  /// Check if this is a Shopify payment response
  bool get isShopifyPayment => checkoutUrl != null;

  @override
  String toString() {
    return 'UnifiedOrderResponse(success: $success, orderId: $orderId, '
           'paymentIntentId: $paymentIntentId, checkoutUrl: $checkoutUrl, '
           'amount: $amount, currency: $currency, status: $status)';
  }
}
