import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/network/api_config.dart';
import '../models/payment_status_model.dart';
import '../../domain/exceptions/payment_exceptions.dart';

/// Unified status data source for checking payment status
/// Uses the unified status endpoint instead of payment-method-specific endpoints
abstract class UnifiedStatusDataSource {
  /// Check payment status using order ID (unified approach)
  Future<PaymentStatusModel> getOrderStatus({
    required String orderId,
    required String authToken,
    String? paymentMethod, // Optional payment method for better performance
  });

  /// Check payment status using payment intent ID (for backward compatibility)
  Future<PaymentStatusModel> getPaymentIntentStatus({
    required String paymentIntentId,
    required String authToken,
    String? paymentMethod, // Optional payment method for better performance
  });
}

/// Implementation of unified status data source
class UnifiedStatusDataSourceImpl implements UnifiedStatusDataSource {
  final Dio _dio;

  UnifiedStatusDataSourceImpl({required Dio dio}) : _dio = dio;

  @override
  Future<PaymentStatusModel> getOrderStatus({
    required String orderId,
    required String authToken,
    String? paymentMethod,
  }) async {
    try {
      debugPrint('[UnifiedStatusDataSource] Checking order status: $orderId');
      debugPrint('[UnifiedStatusDataSource] Using unified status endpoint');
      if (paymentMethod != null) {
        debugPrint('[UnifiedStatusDataSource] Payment method: $paymentMethod (explicit parameter)');
      }

      // Use enhanced URL with payment method if provided for better performance
      final statusUrl = paymentMethod != null
          ? ApiConfig.unifiedStatusUrlWithMethod(orderId, paymentMethod)
          : ApiConfig.unifiedStatusUrl(orderId);

      // Status checks should not be retried for client errors (4xx)
      // Only retry on network/connection issues, not validation errors
      final response = await _dio.get(
        statusUrl,
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
          },
        ),
      );

      debugPrint('[UnifiedStatusDataSource] Status response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        final data = responseData['data'] ?? responseData;
        
        debugPrint('[UnifiedStatusDataSource] Status data: $data');
        return PaymentStatusModel.fromJson(data);
      } else {
        throw PaymentNetworkException(
          message: 'HTTP ${response.statusCode}: Failed to get order status',
        );
      }
    } on DioException catch (e) {
      debugPrint('[UnifiedStatusDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[UnifiedStatusDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  @override
  Future<PaymentStatusModel> getPaymentIntentStatus({
    required String paymentIntentId,
    required String authToken,
    String? paymentMethod,
  }) async {
    try {
      debugPrint('[UnifiedStatusDataSource] Checking payment intent status: $paymentIntentId');
      debugPrint('[UnifiedStatusDataSource] Using unified status endpoint with payment intent ID');
      if (paymentMethod != null) {
        debugPrint('[UnifiedStatusDataSource] Payment method: $paymentMethod (explicit parameter)');
      }

      // Use enhanced URL with payment method if provided for better performance
      final statusUrl = paymentMethod != null
          ? ApiConfig.unifiedStatusUrlWithMethod(paymentIntentId, paymentMethod)
          : ApiConfig.unifiedStatusUrl(paymentIntentId);

      // Status checks should not be retried for client errors (4xx)
      // Only retry on network/connection issues, not validation errors
      final response = await _dio.get(
        statusUrl,
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
          },
        ),
      );

      debugPrint('[UnifiedStatusDataSource] Status response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        final data = responseData['data'] ?? responseData;
        
        debugPrint('[UnifiedStatusDataSource] Status data: $data');
        return PaymentStatusModel.fromJson(data);
      } else {
        throw PaymentNetworkException(
          message: 'HTTP ${response.statusCode}: Failed to get payment intent status',
        );
      }
    } on DioException catch (e) {
      debugPrint('[UnifiedStatusDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[UnifiedStatusDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Handle Dio exceptions and convert to payment exceptions
  PaymentException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const PaymentNetworkException(
          message: 'Request timeout. Please check your connection and try again.',
        );
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final responseData = e.response?.data;
        
        if (statusCode == 400) {
          final errorMessage = _extractErrorMessage(responseData) ?? 'Invalid request data';
          return PaymentValidationException(message: errorMessage);
        } else if (statusCode == 401) {
          return const PaymentAuthException(message: 'Authentication required');
        } else if (statusCode == 404) {
          return const PaymentNetworkException(message: 'Status endpoint not found - using unified status endpoint');
        } else if (statusCode == 422) {
          final errorMessage = _extractErrorMessage(responseData) ?? 'Payment status check failed: wrong number of arguments';
          return PaymentValidationException(message: errorMessage);
        } else if (statusCode != null && statusCode >= 500) {
          return const PaymentServerException(message: 'Server error. Please try again later.');
        } else {
          final errorMessage = _extractErrorMessage(responseData) ?? 'Status check failed';
          return PaymentNetworkException(message: errorMessage);
        }
      case DioExceptionType.cancel:
        return const PaymentNetworkException(message: 'Request was cancelled');
      case DioExceptionType.connectionError:
        return const PaymentNetworkException(
          message: 'Connection error. Please check your internet connection.',
        );
      default:
        return PaymentNetworkException(message: 'Network error: ${e.message}');
    }
  }

  /// Extract error message from response data
  String? _extractErrorMessage(dynamic responseData) {
    if (responseData is Map<String, dynamic>) {
      return responseData['error'] ?? 
             responseData['message'] ?? 
             responseData['errors']?.toString();
    }
    return responseData?.toString();
  }
}
