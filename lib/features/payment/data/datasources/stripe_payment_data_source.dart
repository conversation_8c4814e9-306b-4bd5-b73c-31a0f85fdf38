import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/network/api_config.dart';
import '../models/stripe_config_model.dart';
import '../../domain/exceptions/payment_exceptions.dart';

/// Data source for Stripe configuration
/// Only handles Stripe configuration fetching - payment creation is handled by unified order service
abstract class StripePaymentDataSource {
  /// Get Stripe configuration
  Future<StripeConfigModel> getStripeConfig({
    required String authToken,
  });
}

/// Implementation of Stripe configuration data source
class StripePaymentDataSourceImpl implements StripePaymentDataSource {
  final Dio _dio;

  StripePaymentDataSourceImpl({required Dio dio}) : _dio = dio;

  @override
  Future<StripeConfigModel> getStripeConfig({
    required String authToken,
  }) async {
    try {
      debugPrint('[StripePaymentDataSource] Getting Stripe configuration');

      final response = await _dio.get(
        ApiConfig.stripeConfigUrl,
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
          },
        ),
      );

      debugPrint('[StripePaymentDataSource] Config response: ${response.statusCode}');

      if (response.statusCode == 200) {
        return StripeConfigModel.fromJson(response.data);
      } else {
        throw PaymentNetworkException(
          message: 'HTTP ${response.statusCode}: Failed to get Stripe configuration',
        );
      }
    } on DioException catch (e) {
      debugPrint('[StripePaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[StripePaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Handle Dio exceptions and convert to payment exceptions
  PaymentException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return PaymentNetworkException(message: 'Request timeout: ${e.message}');
      case DioExceptionType.connectionError:
        return PaymentNetworkException(message: 'Connection error: ${e.message}');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? e.message;
        if (statusCode == 401) {
          return PaymentAuthenticationException(message: 'Authentication failed: $message');
        } else if (statusCode == 400) {
          return PaymentValidationException(message: 'Validation error: $message');
        } else if (statusCode == 422) {
          // Handle 422 Unprocessable Entity - likely backend validation error
          final errorMessage = e.response?.data?['error'] ??
                              e.response?.data?['message'] ??
                              'Backend validation error - payment may not be supported for this cart type';
          debugPrint('[StripePaymentDataSource] 422 Error details: ${e.response?.data}');
          return PaymentServerException(message: errorMessage);
        } else {
          return PaymentServerException(message: 'Server error ($statusCode): $message');
        }
      default:
        return PaymentNetworkException(message: 'Network error: ${e.message}');
    }
  }
}
