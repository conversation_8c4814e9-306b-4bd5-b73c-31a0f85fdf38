import 'package:flutter/foundation.dart';
import '../../../../core/network/backend_auth_service.dart';
import '../models/backend_auth_response_model.dart';

/// Abstract interface for backend authentication data source
abstract class BackendAuthDataSource {
  Future<BackendAuthResponseModel> authenticate(String firebaseIdToken);
  Future<BackendAuthResponseModel> refreshToken(String currentBackendToken);
  Future<bool> validateToken(String backendToken);
  Future<void> signOut(String? backendToken);
}

/// Implementation of backend authentication data source
class BackendAuthDataSourceImpl implements BackendAuthDataSource {
  final BackendAuthService _backendAuthService;

  BackendAuthDataSourceImpl({
    BackendAuthService? backendAuthService,
  }) : _backendAuthService = backendAuthService ?? BackendAuthService();

  @override
  Future<BackendAuthResponseModel> authenticate(String firebaseIdToken) async {
    try {
      debugPrint('[BackendAuthDataSource] Authenticating with backend...');
      
      final response = await _backendAuthService.authenticate(firebaseIdToken);
      
      return BackendAuthResponseModel.fromBackendResponse(response);
    } on BackendAuthException catch (e) {
      debugPrint('[BackendAuthDataSource] Backend auth exception: $e');
      rethrow;
    } catch (e) {
      debugPrint('[BackendAuthDataSource] Unexpected error: $e');
      throw BackendAuthException(
        'Unexpected error during backend authentication: $e',
        originalError: e,
      );
    }
  }

  @override
  Future<BackendAuthResponseModel> refreshToken(String currentBackendToken) async {
    // TODO: Implement token refresh when backend supports it
    throw UnimplementedError('Token refresh not yet implemented');
  }

  @override
  Future<bool> validateToken(String backendToken) async {
    try {
      debugPrint('[BackendAuthDataSource] Validating backend token...');
      debugPrint('[BackendAuthDataSource] Token length: ${backendToken.length}');

      final response = await _backendAuthService.verifyToken(backendToken);

      // Check if token is valid based on API response structure: {success: true, data: {valid: true, user_id: '...'}}
      final dataValid = response.data?['valid'] as bool?;
      final isValid = response.success && (dataValid == true);
      debugPrint('[BackendAuthDataSource] Token validation result: $isValid');

      if (isValid) {
        final userId = response.data?['user_id'] as String?;
        debugPrint('[BackendAuthDataSource] Token is valid - data.valid: $dataValid, user_id: $userId');
      } else {
        debugPrint('[BackendAuthDataSource] Token is invalid - success: ${response.success}, data.valid: $dataValid');
      }

      return isValid;
    } on BackendAuthException catch (e) {
      debugPrint('[BackendAuthDataSource] Token validation failed with BackendAuthException: $e');
      debugPrint('[BackendAuthDataSource] Status code: ${e.statusCode}');

      // If it's a 401 error or other auth-related errors, token is invalid but this is expected behavior
      if (e.statusCode == 401 ||
          e.statusCode == 403 ||
          e.message.toLowerCase().contains('unauthorized') ||
          e.message.toLowerCase().contains('invalid') ||
          e.message.toLowerCase().contains('expired')) {
        debugPrint('[BackendAuthDataSource] Token is expired/invalid (expected behavior)');
        return false;
      }

      // For other errors (network, server errors), rethrow so they can be handled appropriately
      debugPrint('[BackendAuthDataSource] Rethrowing non-auth error: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('[BackendAuthDataSource] Unexpected error during token validation: $e');
      throw BackendAuthException(
        'Unexpected error during token validation: $e',
        originalError: e,
      );
    }
  }

  @override
  Future<void> signOut(String? backendToken) async {
    // TODO: Implement backend sign out when backend supports it
    debugPrint('[BackendAuthDataSource] Backend sign out not yet implemented');
  }
}
